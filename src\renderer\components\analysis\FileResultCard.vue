<template>
  <div class="file-result mb-lg">
    <!-- File Header -->
    <div class="file-header">
      <div class="flex justify-between items-center">
        <div>
          <h3 class="font-medium text-lg">{{ getFileName(result.filePath) }}</h3>
          <div class="file-path text-sm text-muted font-mono">{{ result.filePath }}</div>
          <div class="file-stats text-sm text-muted mt-xs">
            {{ filteredResourcesCount }} resources • 
            {{ formatFileSize(result.fileSize) }} • 
            {{ getCategoryDisplayName(result.category) }}
            <span v-if="result.isOverride" class="badge badge-warning ml-sm">Override</span>
            <span v-if="result.subcategory" class="badge badge-info ml-sm">{{ formatSubcategory(result.subcategory) }}</span>
          </div>
          
          <!-- Enhanced metadata display -->
          <div v-if="hasMetadata" class="file-metadata text-sm mt-sm">
            <div v-if="result.suggestedPath" class="metadata-item">
              <strong>Suggested folder:</strong> {{ result.suggestedPath }}
            </div>
            
            <!-- Deep Analysis Results -->
            <div v-if="hasDeepAnalysis" class="deep-analysis-section">
              <div v-if="result.metadata.deepAnalysis" class="metadata-item">
                <strong>Detailed Analysis:</strong> {{ result.metadata.deepAnalysis.detailedDescription }}
              </div>

              <!-- CAS-specific info -->
              <div v-if="result.metadata.deepAnalysis?.casInfo" class="cas-info mt-xs">
                <div class="metadata-item">
                  <strong>CAS Category:</strong> {{ formatSubcategory(result.metadata.deepAnalysis.casInfo.category) }}
                </div>
                <div v-if="result.metadata.deepAnalysis.casInfo.genders.length > 0" class="metadata-item">
                  <strong>Gender:</strong> {{ result.metadata.deepAnalysis.casInfo.genders.map(g => formatSubcategory(g)).join(', ') }}
                </div>
                <div v-if="result.metadata.deepAnalysis.casInfo.ageGroups.length > 0" class="metadata-item">
                  <strong>Age Groups:</strong> {{ result.metadata.deepAnalysis.casInfo.ageGroups.map(a => formatSubcategory(a)).join(', ') }}
                </div>
              </div>

              <!-- Object-specific info -->
              <div v-if="result.metadata.deepAnalysis?.objectInfo" class="object-info mt-xs">
                <div class="metadata-item">
                  <strong>Object Function:</strong> {{ formatSubcategory(result.metadata.deepAnalysis.objectInfo.function) }}
                </div>
                <div v-if="result.metadata.deepAnalysis.objectInfo.roomAssignment.length > 0" class="metadata-item">
                  <strong>Room Assignment:</strong> {{ result.metadata.deepAnalysis.objectInfo.roomAssignment.map(r => formatSubcategory(r)).join(', ') }}
                </div>
              </div>

              <!-- Script-specific info -->
              <div v-if="result.metadata.deepAnalysis?.scriptInfo" class="script-info mt-xs">
                <div class="metadata-item">
                  <strong>Script Type:</strong> {{ formatSubcategory(result.metadata.deepAnalysis.scriptInfo.category) }}
                  <span v-if="result.metadata.deepAnalysis.scriptInfo.isFramework" class="badge badge-primary ml-sm">Framework</span>
                </div>
                <div v-if="result.metadata.deepAnalysis.scriptInfo.gameplayAreas.length > 0" class="metadata-item">
                  <strong>Affects:</strong> {{ result.metadata.deepAnalysis.scriptInfo.gameplayAreas.join(', ') }}
                </div>
              </div>

              <!-- Suggested tags -->
              <div v-if="result.metadata.deepAnalysis?.suggestedTags?.length > 0" class="metadata-item">
                <strong>Suggested Tags:</strong>
                <span v-for="tag in result.metadata.deepAnalysis.suggestedTags" :key="tag" class="badge badge-secondary ml-xs">
                  {{ tag }}
                </span>
              </div>
            </div>
            
            <div v-if="result.dependencies.length > 0" class="metadata-item">
              <strong>Dependencies:</strong> {{ result.dependencies.join(', ') }}
            </div>
            <div v-if="result.conflicts.length > 0" class="metadata-item text-warning">
              <strong>Potential conflicts:</strong> {{ result.conflicts.join(', ') }}
            </div>
          </div>
        </div>
        <button 
          class="btn btn-secondary btn-sm"
          @click="handleToggleExpanded"
        >
          {{ isExpanded ? 'Collapse' : 'Expand' }}
        </button>
      </div>
    </div>

    <!-- Resources Table Slot -->
    <slot v-if="isExpanded" name="resources-table" />
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import type { AnalyzedPackage } from '../../../types/analysis';

// Props
interface Props {
  result: AnalyzedPackage;
  isExpanded: boolean;
  filteredResourcesCount: number;
}

const props = defineProps<Props>();

// Events
interface Emits {
  (e: 'toggle-expanded'): void;
}

const emit = defineEmits<Emits>();

// Computed
const hasMetadata = computed(() => 
  props.result.dependencies.length > 0 || 
  props.result.conflicts.length > 0 || 
  props.result.suggestedPath || 
  hasDeepAnalysis.value
);

const hasDeepAnalysis = computed(() => 
  !!(props.result.metadata?.deepAnalysis && (
    props.result.metadata.deepAnalysis.casInfo ||
    props.result.metadata.deepAnalysis.objectInfo ||
    props.result.metadata.deepAnalysis.scriptInfo ||
    props.result.metadata.deepAnalysis.detailedDescription !== 'Unknown content' ||
    (props.result.metadata.deepAnalysis.suggestedTags && props.result.metadata.deepAnalysis.suggestedTags.length > 0)
  ))
);

// Methods
function handleToggleExpanded() {
  emit('toggle-expanded');
}

function getFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || filePath;
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getCategoryDisplayName(category: string): string {
  const displayNames: Record<string, string> = {
    'cas_cc': 'CAS Custom Content',
    'build_buy_cc': 'Build/Buy Custom Content',
    'script_mod': 'Script Mod',
    'tuning_mod': 'Tuning Mod',
    'override': 'Override',
    'framework': 'Framework',
    'library': 'Library',
    'unknown': 'Unknown'
  };
  
  return displayNames[category] || category;
}

function formatSubcategory(subcategory: string): string {
  return subcategory
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}
</script>

<style scoped>
.file-result {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.file-header {
  padding: var(--spacing-lg);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
}

.file-path {
  word-break: break-all;
}

.file-metadata {
  background-color: var(--bg-tertiary);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--primary-color);
}

.metadata-item {
  margin-bottom: var(--spacing-xs);
}

.metadata-item:last-child {
  margin-bottom: 0;
}

.text-warning {
  color: var(--warning-color);
}

.ml-sm {
  margin-left: var(--spacing-sm);
}
</style>
</script>
