/**
 * Simple Enhanced DDS Processing Test
 * Tests the enhanced DDS processing by running full analysis on problematic files
 */

import { OptimizedAnalysisEngine } from '../services/analysis/OptimizedAnalysisEngine';
import * as fs from 'fs';
import * as path from 'path';

async function testEnhancedDdsSimple(): Promise<void> {
    console.log('🧪 Simple Enhanced DDS Processing Test');
    console.log('=' .repeat(60));

    // Test with files that had "Non-DXT DDS images cannot be converted" errors
    const testFiles = [
        'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods\\FAIRYLICIOUS\\FAIRYLICIOUS_High_Bedding.package'
    ];

    for (const filePath of testFiles) {
        if (!fs.existsSync(filePath)) {
            console.log(`⚠️ File not found: ${path.basename(filePath)}`);
            continue;
        }

        console.log(`\n📦 Testing: ${path.basename(filePath)}`);
        console.log('-'.repeat(50));

        const startTime = performance.now();

        try {
            // Read the file
            const buffer = fs.readFileSync(filePath);
            console.log(`📊 File size: ${(buffer.length / 1024).toFixed(1)} KB`);

            // Run optimized analysis with thumbnail extraction
            const analysisResult = await OptimizedAnalysisEngine.analyze(
                buffer,
                path.basename(filePath),
                {
                    enableStreaming: false,
                    enableParallelProcessing: false,
                    maxConcurrentOperations: 1,
                    enableAggressiveCaching: false,
                    enableMemoryOptimization: true,
                    targetProcessingTime: 5000,
                    enableProgressiveResults: false,
                    prioritizeEssentialFeatures: false
                }
            );

            const processingTime = performance.now() - startTime;

            console.log(`✅ Analysis completed in ${processingTime.toFixed(2)}ms`);
            console.log(`📊 Resources found: ${analysisResult.resourceCount}`);

            if (analysisResult.thumbnails && analysisResult.thumbnails.length > 0) {
                console.log(`🎨 Thumbnails extracted: ${analysisResult.thumbnails.length}`);
                
                // Check for enhanced processing
                let enhancedCount = 0;
                let dstCount = 0;
                let errorCount = 0;

                analysisResult.thumbnails.forEach((thumbnail, index) => {
                    if (thumbnail.resourceType.includes('DST')) {
                        dstCount++;
                        
                        if (thumbnail.resourceType.includes('Enhanced')) {
                            enhancedCount++;
                            console.log(`   ✨ Enhanced: ${thumbnail.width}x${thumbnail.height} (${thumbnail.extractionMethod})`);
                        } else if (thumbnail.isFallback) {
                            errorCount++;
                            console.log(`   ⚠️ Fallback: ${thumbnail.width}x${thumbnail.height} (${thumbnail.extractionMethod})`);
                        } else {
                            console.log(`   ✅ Standard: ${thumbnail.width}x${thumbnail.height} (${thumbnail.extractionMethod})`);
                        }
                    }
                });

                console.log(`\n📈 DST Processing Summary:`);
                console.log(`   DST Images: ${dstCount}`);
                console.log(`   Enhanced Processing: ${enhancedCount}`);
                console.log(`   Fallbacks: ${errorCount}`);
                console.log(`   Success Rate: ${dstCount > 0 ? (((dstCount - errorCount) / dstCount) * 100).toFixed(1) : 0}%`);

                if (enhancedCount > 0) {
                    console.log(`🎯 Enhanced DDS processing successfully handled ${enhancedCount} images!`);
                }

            } else {
                console.log(`⚠️ No thumbnails extracted`);
            }

        } catch (error) {
            console.log(`❌ Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    console.log('\n🏁 Simple test completed!');
}

// Run the test
if (require.main === module) {
    testEnhancedDdsSimple().catch(error => {
        console.error('Test failed:', error);
        process.exit(1);
    });
}

export { testEnhancedDdsSimple };
