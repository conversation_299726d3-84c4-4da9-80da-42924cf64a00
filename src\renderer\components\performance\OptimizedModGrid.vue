<template>
  <div class="optimized-mod-grid" ref="containerRef">
    <!-- Performance Monitoring Panel (Development) -->
    <div v-if="showPerformancePanel" class="performance-panel">
      <div class="performance-metrics">
        <span class="metric">
          Render: {{ performanceMetrics.renderTime.toFixed(1) }}ms
        </span>
        <span class="metric">
          FPS: {{ performanceMetrics.frameRate }}
        </span>
        <span class="metric">
          Memory: {{ (performanceMetrics.memoryUsage / 1024 / 1024).toFixed(1) }}MB
        </span>
        <span class="metric" :class="{ 'metric--warning': !isPerformanceOptimal }">
          Score: {{ performanceScore }}%
        </span>
      </div>
    </div>

    <!-- Virtual Scrolling Container -->
    <div 
      class="virtual-container"
      :style="{ height: `${containerHeight}px` }"
      @scroll="handleScroll"
    >
      <!-- Virtual Spacer (Top) -->
      <div :style="{ height: `${topSpacerHeight}px` }"></div>
      
      <!-- Visible Items Grid -->
      <div 
        class="virtual-grid"
        :class="[
          `virtual-grid--${thumbnailSize}`,
          { 'virtual-grid--loading': isLoading }
        ]"
        :style="gridStyles"
      >
        <!-- Optimized Mod Cards - Placeholder for now -->
        <div
          v-for="item in visibleItems"
          :key="`${item.item.fileName || item.item.filePath || item.index}`"
          class="optimized-mod-card-placeholder"
          :style="{
            transform: `translateY(${item.top}px)`,
            position: 'absolute',
            width: '100%',
            height: `${gridConfig.itemHeight}px`
          }"
          @click="handleModClick(item.item)"
        >
          <div class="placeholder-content">
            <div class="placeholder-thumbnail"></div>
            <div class="placeholder-text">
              <div class="placeholder-title">{{ item.item.modName || item.item.fileName }}</div>
              <div class="placeholder-author">{{ item.item.author || 'Unknown' }}</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Virtual Spacer (Bottom) -->
      <div :style="{ height: `${bottomSpacerHeight}px` }"></div>
    </div>

    <!-- Loading Skeleton -->
    <div v-if="isLoading" class="loading-skeleton">
      <div 
        v-for="n in skeletonCount" 
        :key="n" 
        class="skeleton-item"
        :class="`skeleton-item--${thumbnailSize}`"
      ></div>
    </div>

    <!-- Empty State -->
    <div v-if="!isLoading && mods.length === 0" class="empty-state">
      <div class="empty-state__icon">
        <FolderOpenIcon />
      </div>
      <h3 class="empty-state__title">No mods found</h3>
      <p class="empty-state__description">
        {{ hasActiveFilters ? 
          'Try adjusting your filters to see more results.' : 
          'Start by analyzing your Sims 4 mods folder.' }}
      </p>
    </div>

    <!-- Performance Alerts -->
    <div v-if="criticalAlerts.length > 0" class="performance-alerts">
      <div 
        v-for="alert in criticalAlerts.slice(0, 3)" 
        :key="alert.timestamp"
        class="performance-alert"
        :class="`performance-alert--${alert.type}`"
      >
        {{ alert.message }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import { FolderOpenIcon } from '@heroicons/vue/24/outline';

// Import performance composables
import { useVirtualScrolling } from '../../composables/useVirtualScrolling';
import { usePerformanceMonitoring } from '../../composables/usePerformanceMonitoring';
import { useMemoryOptimization } from '../../composables/useMemoryOptimization';
import { useProgressiveLoading } from '../../composables/useProgressiveLoading';

// Import optimized components
// import OptimizedModCard from './OptimizedModCard.vue';

// Types
import type { ModData } from '../../../types/ModData';
import type { ThumbnailSize, ViewMode } from '../../types/dashboard';

interface OptimizedModGridProps {
  mods: ModData[];
  thumbnailSize: ThumbnailSize;
  viewMode: ViewMode;
  containerHeight: number;
  isLoading?: boolean;
  hasActiveFilters?: boolean;
  showPerformancePanel?: boolean;
}

interface OptimizedModGridEmits {
  'mod-click': [mod: ModData];
  'performance-alert': [alert: any];
}

// Props
const props = withDefaults(defineProps<OptimizedModGridProps>(), {
  isLoading: false,
  hasActiveFilters: false,
  showPerformancePanel: false,
});

// Emits
const emit = defineEmits<OptimizedModGridEmits>();

// Refs
const containerRef = ref<HTMLElement>();

// Performance monitoring
const {
  metrics: performanceMetrics,
  isPerformanceOptimal,
  performanceScore,
  criticalAlerts,
  measureRenderTime,
  measureInteractionDelay,
} = usePerformanceMonitoring({
  maxRenderTime: 45,
  maxMemoryUsage: 100 * 1024 * 1024, // 100MB
  minFrameRate: 30,
  maxInteractionDelay: 100,
});

// Memory optimization
const {
  cache,
  createOptimizedArray,
  getMemoryReport,
} = useMemoryOptimization({
  maxCacheSize: 1000,
  cleanupInterval: 30000,
});

// Progressive loading
const progressiveLoading = useProgressiveLoading({
  rootMargin: '100px',
  threshold: 0.1,
  batchSize: 8,
  enablePrefetch: true,
});

// Grid configuration
const gridConfig = computed(() => {
  const configs = {
    xs: { itemHeight: 200, columns: 6, gap: 12 },
    sm: { itemHeight: 240, columns: 5, gap: 14 },
    md: { itemHeight: 280, columns: 4, gap: 16 },
    lg: { itemHeight: 320, columns: 3, gap: 18 },
    xl: { itemHeight: 360, columns: 2, gap: 20 },
  };
  return configs[props.thumbnailSize];
});

// Virtual scrolling
const {
  visibleItems,
  totalHeight,
  handleScroll,
  scrollToIndex,
  getPerformanceReport: getVirtualScrollReport,
} = useVirtualScrolling(props.mods, {
  itemHeight: gridConfig.value.itemHeight,
  containerHeight: props.containerHeight,
  overscan: 5,
});

// Computed properties
const topSpacerHeight = computed(() => {
  const firstVisible = visibleItems.value[0];
  return firstVisible ? firstVisible.top : 0;
});

const bottomSpacerHeight = computed(() => {
  const lastVisible = visibleItems.value[visibleItems.value.length - 1];
  if (!lastVisible) return 0;
  
  const lastItemBottom = lastVisible.top + lastVisible.height;
  return Math.max(0, totalHeight.value - lastItemBottom);
});

const gridStyles = computed(() => {
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${gridConfig.value.columns}, 1fr)`,
    gap: `${gridConfig.value.gap}px`,
    position: 'relative',
  };
});

const skeletonCount = computed(() => {
  return Math.min(20, Math.ceil(props.containerHeight / gridConfig.value.itemHeight) * gridConfig.value.columns);
});

// Event handlers
const handleModClick = (mod: ModData) => {
  measureInteractionDelay(() => {
    emit('mod-click', mod);
  });
};

const handleItemLoad = (index: number) => {
  // Update performance metrics
  performanceMetrics.componentCount++;
};

const handleItemError = (index: number, error: Error) => {
  console.warn(`🚨 [OptimizedModGrid] Item ${index} load error:`, error);
};

// Performance optimization
const optimizeRendering = async () => {
  await measureRenderTime(async () => {
    await nextTick();
    
    // Update component count
    performanceMetrics.componentCount = visibleItems.value.length;
    
    // Cache visible items for faster access
    visibleItems.value.forEach((item, index) => {
      const cacheKey = `item-${item.index}`;
      cache.set(cacheKey, item.item);
    });
  });
};

// Watch for performance alerts
watch(criticalAlerts, (alerts) => {
  if (alerts.length > 0) {
    emit('performance-alert', alerts[0]);
  }
});

// Watch for mods changes and optimize
watch(() => props.mods, async () => {
  await optimizeRendering();
}, { immediate: true });

// Watch for thumbnail size changes
watch(() => props.thumbnailSize, async () => {
  await optimizeRendering();
});

// Lifecycle
onMounted(async () => {
  await nextTick();
  await optimizeRendering();
  
  // Log performance report
  console.log('📊 [OptimizedModGrid] Performance Report:', {
    virtualScroll: getVirtualScrollReport(),
    memory: getMemoryReport(),
    performance: performanceMetrics,
  });
});

onUnmounted(() => {
  // Cleanup is handled by composables
});
</script>

<style scoped>
/* Import design system */
@import '../../styles/simonitor-design-system.css';

.optimized-mod-grid {
  position: relative;
  width: 100%;
  height: 100%;
}

.performance-panel {
  position: fixed;
  top: 10px;
  right: 10px;
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  font-size: var(--text-xs);
  z-index: 1000;
  box-shadow: var(--shadow-md);
}

.performance-metrics {
  display: flex;
  gap: var(--space-2);
}

.metric {
  color: var(--text-secondary);
  font-weight: var(--font-medium);
}

.metric--warning {
  color: var(--warning);
}

.virtual-container {
  width: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.virtual-grid {
  width: 100%;
  position: relative;
  transition: opacity var(--duration-200) var(--ease-out);
}

.virtual-grid--loading {
  opacity: 0.7;
}

.loading-skeleton {
  display: grid;
  gap: 16px;
  padding: var(--space-4);
}

.skeleton-item {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: var(--radius-md);
}

.skeleton-item--xs { height: 180px; }
.skeleton-item--sm { height: 220px; }
.skeleton-item--md { height: 260px; }
.skeleton-item--lg { height: 300px; }
.skeleton-item--xl { height: 340px; }

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-12);
  text-align: center;
  color: var(--text-muted);
}

.empty-state__icon {
  width: 64px;
  height: 64px;
  margin-bottom: var(--space-4);
  opacity: 0.5;
}

.empty-state__title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
  color: var(--text-secondary);
}

.empty-state__description {
  font-size: var(--text-base);
  max-width: 400px;
}

.performance-alerts {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.performance-alert {
  padding: var(--space-3);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  box-shadow: var(--shadow-lg);
  max-width: 300px;
}

.performance-alert--warning {
  background: var(--warning-bg);
  color: var(--warning);
  border: 1px solid var(--warning);
}

.performance-alert--error {
  background: var(--error-bg);
  color: var(--error);
  border: 1px solid var(--error);
}

.performance-alert--critical {
  background: var(--error);
  color: var(--error-foreground);
  border: 1px solid var(--error);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Placeholder card styles */
.optimized-mod-card-placeholder {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  cursor: pointer;
  transition: all var(--duration-200) var(--ease-out);
}

.optimized-mod-card-placeholder:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

.placeholder-content {
  display: flex;
  gap: var(--space-3);
  height: 100%;
}

.placeholder-thumbnail {
  width: 60px;
  height: 60px;
  background: var(--bg-secondary);
  border-radius: var(--radius-sm);
  flex-shrink: 0;
}

.placeholder-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.placeholder-title {
  font-weight: var(--font-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.placeholder-author {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}
</style>
