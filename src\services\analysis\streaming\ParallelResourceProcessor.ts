/**
 * Parallel Resource Processor
 * High-performance parallel processing of package resources
 * Uses worker-like parallel processing within the main thread for optimal performance
 */

import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import { ModCategory } from '../../../types/analysis';
import type { CancellationToken } from '../../../types/analysis-results';

export interface ParallelProcessingOptions {
    maxConcurrent: number;
    enableCaching: boolean;
    prioritizeEssential: boolean;
    enableResourceValidation: boolean;
    enableSpecializedAnalysis: boolean;
    timeoutPerResource: number;
}

export interface ParallelProcessingResult {
    category: ModCategory;
    subcategory: string;
    isOverride: boolean;
    resources: any[];
    specializedResources: any[];
    validation: {
        isValid: boolean;
        issues: string[];
        resourcesValidated: number;
        validationTime: number;
    };
    parallelOperations: number;
    processingMetrics: {
        totalResources: number;
        processedResources: number;
        skippedResources: number;
        averageProcessingTime: number;
        parallelEfficiency: number;
    };
}

/**
 * Parallel resource processor for optimal performance
 */
export class ParallelResourceProcessor {
    private static readonly BATCH_SIZE = 10;
    private static readonly MAX_PROCESSING_TIME = 50; // ms per resource
    
    // Resource type priorities for processing order
    private static readonly PRIORITY_TYPES = new Map([
        [0x034AEECB, 10], // CAS Part - highest priority
        [0x0354796A, 9],  // CAS Modifier
        [0x3C1AF1F2, 8],  // CAS Thumbnail
        [0x319E4F1D, 7],  // Object Definition
        [0x62E94D38, 6],  // Tuning
        [0x220557DA, 5],  // SimData
        [0x00B2D882, 4],  // DST Image
        [0x2E75C764, 3],  // PNG Image
        [0x0C772E27, 2],  // Object Catalog
        [0x00AE6C67, 1]   // CAS Preset
    ]);

    /**
     * Process resources in parallel batches
     */
    public async processResourcesParallel(
        s4tkPackage: Package,
        options: Partial<ParallelProcessingOptions> = {},
        cancellationToken?: CancellationToken
    ): Promise<ParallelProcessingResult> {
        const startTime = performance.now();
        
        const opts: ParallelProcessingOptions = {
            maxConcurrent: 4,
            enableCaching: true,
            prioritizeEssential: true,
            enableResourceValidation: true,
            enableSpecializedAnalysis: true,
            timeoutPerResource: this.MAX_PROCESSING_TIME,
            ...options
        };

        const result: ParallelProcessingResult = {
            category: ModCategory.UNKNOWN,
            subcategory: 'unknown',
            isOverride: false,
            resources: [],
            specializedResources: [],
            validation: {
                isValid: true,
                issues: [],
                resourcesValidated: 0,
                validationTime: 0
            },
            parallelOperations: 0,
            processingMetrics: {
                totalResources: s4tkPackage.size,
                processedResources: 0,
                skippedResources: 0,
                averageProcessingTime: 0,
                parallelEfficiency: 0
            }
        };

        try {
            // Get prioritized resource list
            const resourceEntries = this.prioritizeResources(s4tkPackage, opts);
            
            if (resourceEntries.length === 0) {
                return result;
            }

            // Process in parallel batches
            const batchResults = await this.processBatches(
                resourceEntries,
                opts,
                cancellationToken
            );

            // Aggregate results
            this.aggregateResults(batchResults, result);

            // Determine category and override status
            this.determineCategoryAndOverride(batchResults, result);

            // Calculate metrics
            this.calculateMetrics(batchResults, result, performance.now() - startTime);

            return result;

        } catch (error) {
            console.warn('Parallel processing failed, using fallback:', error);
            return this.fallbackProcessing(s4tkPackage, opts);
        }
    }

    /**
     * Prioritize resources for processing
     */
    private prioritizeResources(
        s4tkPackage: Package,
        options: ParallelProcessingOptions
    ): Array<{ entry: ResourceEntry; priority: number; key: any }> {
        const resources: Array<{ entry: ResourceEntry; priority: number; key: any }> = [];

        for (const [key, entry] of s4tkPackage.entries()) {
            const priority = this.PRIORITY_TYPES.get(key.type) || 0;
            
            // Skip low-priority resources if prioritizing essential
            if (options.prioritizeEssential && priority === 0) {
                continue;
            }

            resources.push({ entry, priority, key });
        }

        // Sort by priority (highest first)
        resources.sort((a, b) => b.priority - a.priority);

        // Limit resources for performance
        const maxResources = options.prioritizeEssential ? 50 : 200;
        return resources.slice(0, maxResources);
    }

    /**
     * Process resources in parallel batches
     */
    private async processBatches(
        resources: Array<{ entry: ResourceEntry; priority: number; key: any }>,
        options: ParallelProcessingOptions,
        cancellationToken?: CancellationToken
    ): Promise<any[]> {
        const batchResults: any[] = [];
        const batchSize = Math.min(this.BATCH_SIZE, options.maxConcurrent);

        for (let i = 0; i < resources.length; i += batchSize) {
            if (cancellationToken?.isCancelled) {
                break;
            }

            const batch = resources.slice(i, i + batchSize);
            
            // Process batch in parallel
            const batchPromises = batch.map(({ entry, key, priority }) =>
                this.processResourceWithTimeout(entry, key, priority, options)
            );

            try {
                const batchResult = await Promise.allSettled(batchPromises);
                batchResults.push(...batchResult);
            } catch (error) {
                console.warn('Batch processing error:', error);
                // Continue with next batch
            }

            // Yield control to prevent blocking
            await new Promise(resolve => setImmediate(resolve));
        }

        return batchResults;
    }

    /**
     * Process single resource with timeout
     */
    private async processResourceWithTimeout(
        entry: ResourceEntry,
        key: any,
        priority: number,
        options: ParallelProcessingOptions
    ): Promise<any> {
        const startTime = performance.now();

        return new Promise((resolve) => {
            // Set timeout
            const timeout = setTimeout(() => {
                resolve({
                    status: 'timeout',
                    resourceType: key.type,
                    priority,
                    processingTime: performance.now() - startTime
                });
            }, options.timeoutPerResource);

            try {
                // Quick resource analysis
                const analysis = this.analyzeResourceQuick(entry, key, priority);
                
                clearTimeout(timeout);
                resolve({
                    status: 'success',
                    ...analysis,
                    processingTime: performance.now() - startTime
                });

            } catch (error) {
                clearTimeout(timeout);
                resolve({
                    status: 'error',
                    resourceType: key.type,
                    priority,
                    error: error instanceof Error ? error.message : String(error),
                    processingTime: performance.now() - startTime
                });
            }
        });
    }

    /**
     * Quick resource analysis
     */
    private analyzeResourceQuick(entry: ResourceEntry, key: any, priority: number): any {
        const resourceType = key.type;
        const resourceSize = entry.value?.byteLength || 0;

        return {
            resourceType,
            resourceKey: `${key.type.toString(16)}-${key.group.toString(16)}-${key.instance.toString(16)}`,
            size: resourceSize,
            priority,
            category: this.categorizeResourceType(resourceType),
            isOverride: this.isOverrideResource(resourceType),
            isEssential: priority > 5,
            metadata: {
                type: resourceType.toString(16),
                compressed: resourceSize > 0,
                quickAnalysis: true
            }
        };
    }

    /**
     * Categorize resource type
     */
    private categorizeResourceType(resourceType: number): string {
        // CAS types
        if ([0x034AEECB, 0x0354796A, 0x00AE6C67, 0x3C1AF1F2].includes(resourceType)) {
            return 'CAS';
        }
        
        // Object types
        if ([0x319E4F1D, 0x0C772E27, 0x00B2D882].includes(resourceType)) {
            return 'OBJECTS';
        }
        
        // Gameplay types
        if ([0x62E94D38, 0x220557DA].includes(resourceType)) {
            return 'GAMEPLAY';
        }
        
        return 'UNKNOWN';
    }

    /**
     * Check if resource is an override
     */
    private isOverrideResource(resourceType: number): boolean {
        const overrideTypes = [0x62E94D38, 0x220557DA, 0x0C772E27];
        return overrideTypes.includes(resourceType);
    }

    /**
     * Aggregate batch results
     */
    private aggregateResults(batchResults: any[], result: ParallelProcessingResult): void {
        const successfulResults = batchResults
            .filter(r => r.status === 'fulfilled' && r.value.status === 'success')
            .map(r => r.value);

        result.resources = successfulResults;
        result.processingMetrics.processedResources = successfulResults.length;
        result.processingMetrics.skippedResources = batchResults.length - successfulResults.length;
        result.parallelOperations = Math.min(batchResults.length, 4); // Max concurrent
    }

    /**
     * Determine category and override status
     */
    private determineCategoryAndOverride(batchResults: any[], result: ParallelProcessingResult): void {
        const categories = new Map<string, number>();
        let overrideCount = 0;

        result.resources.forEach(resource => {
            const category = resource.category;
            categories.set(category, (categories.get(category) || 0) + 1);
            
            if (resource.isOverride) {
                overrideCount++;
            }
        });

        // Determine primary category
        let primaryCategory = 'UNKNOWN';
        let maxCount = 0;
        
        for (const [category, count] of categories) {
            if (count > maxCount) {
                maxCount = count;
                primaryCategory = category;
            }
        }

        result.category = primaryCategory as ModCategory;
        result.subcategory = primaryCategory.toLowerCase();
        result.isOverride = overrideCount > 0;
    }

    /**
     * Calculate processing metrics
     */
    private calculateMetrics(batchResults: any[], result: ParallelProcessingResult, totalTime: number): void {
        const processingTimes = result.resources.map(r => r.processingTime || 0);
        const averageTime = processingTimes.length > 0 
            ? processingTimes.reduce((a, b) => a + b, 0) / processingTimes.length 
            : 0;

        result.processingMetrics.averageProcessingTime = averageTime;
        
        // Calculate parallel efficiency (how much faster than sequential)
        const sequentialTime = averageTime * result.resources.length;
        result.processingMetrics.parallelEfficiency = sequentialTime > 0 ? sequentialTime / totalTime : 1;
    }

    /**
     * Fallback processing for errors
     */
    private async fallbackProcessing(
        s4tkPackage: Package,
        options: ParallelProcessingOptions
    ): Promise<ParallelProcessingResult> {
        // Simple sequential processing
        const resourceTypes = new Set<number>();
        let overrideDetected = false;

        // Quick enumeration
        let count = 0;
        for (const [key] of s4tkPackage.entries()) {
            resourceTypes.add(key.type);
            
            if (this.isOverrideResource(key.type)) {
                overrideDetected = true;
            }
            
            count++;
            if (count >= 50) break; // Limit for speed
        }

        // Determine category
        const category = this.determineCategoryFromTypes(Array.from(resourceTypes));

        return {
            category,
            subcategory: category.toLowerCase(),
            isOverride: overrideDetected,
            resources: [],
            specializedResources: [],
            validation: { isValid: true, issues: [], resourcesValidated: count, validationTime: 0 },
            parallelOperations: 0,
            processingMetrics: {
                totalResources: s4tkPackage.size,
                processedResources: count,
                skippedResources: s4tkPackage.size - count,
                averageProcessingTime: 0,
                parallelEfficiency: 1
            }
        };
    }

    /**
     * Determine category from resource types
     */
    private determineCategoryFromTypes(resourceTypes: number[]): ModCategory {
        if (resourceTypes.some(type => [0x034AEECB, 0x0354796A, 0x00AE6C67, 0x3C1AF1F2].includes(type))) {
            return ModCategory.CAS;
        }
        
        if (resourceTypes.some(type => [0x319E4F1D, 0x0C772E27, 0x00B2D882].includes(type))) {
            return ModCategory.OBJECTS;
        }
        
        if (resourceTypes.some(type => [0x62E94D38, 0x220557DA].includes(type))) {
            return ModCategory.GAMEPLAY;
        }
        
        return ModCategory.UNKNOWN;
    }
}
