/**
 * Parallelization Performance Test
 * Tests true parallelization performance with controlled file counts
 */

import { promises as fs } from 'fs';
import * as path from 'path';
import { ElectronWorkerPool } from '../main/workers/ElectronWorkerPool';
import { OptimizedAnalysisEngine } from '../services/analysis/OptimizedAnalysisEngine';

interface PerformanceTestResult {
    fileCount: number;
    workerPoolTime: number;
    directAnalysisTime: number;
    speedupFactor: number;
    avgTimePerFile: {
        workerPool: number;
        directAnalysis: number;
    };
}

async function getTestFiles(count: number): Promise<string[]> {
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    async function collectFiles(dir: string): Promise<string[]> {
        const files: string[] = [];
        const entries = await fs.readdir(dir, { withFileTypes: true });
        
        for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
                files.push(...await collectFiles(fullPath));
            } else if (entry.name.endsWith('.package')) {
                files.push(fullPath);
            }
        }
        return files;
    }
    
    const allFiles = await collectFiles(modsPath);
    return allFiles.slice(0, count);
}

async function testWorkerPoolPerformance(files: string[]): Promise<number> {
    const workerPool = new ElectronWorkerPool(6);
    const startTime = performance.now();
    
    try {
        const promises = files.map(async (filePath) => {
            return await workerPool.analyze(filePath, {
                enableStreaming: true,
                enableParallelProcessing: true,
                maxConcurrentOperations: 4,
                enableAggressiveCaching: true,
                enableMemoryOptimization: true,
                targetProcessingTime: 120,
                enableProgressiveResults: true,
                prioritizeEssentialFeatures: true
            });
        });
        
        await Promise.all(promises);
        return performance.now() - startTime;
    } finally {
        workerPool.terminate();
    }
}

async function testDirectAnalysisPerformance(files: string[]): Promise<number> {
    const startTime = performance.now();
    
    const promises = files.map(async (filePath) => {
        const buffer = await fs.readFile(filePath);
        return await OptimizedAnalysisEngine.analyze(buffer, filePath, {
            enableStreaming: true,
            enableParallelProcessing: true,
            maxConcurrentOperations: 4,
            enableAggressiveCaching: true,
            enableMemoryOptimization: true,
            targetProcessingTime: 120,
            enableProgressiveResults: true,
            prioritizeEssentialFeatures: true
        });
    });
    
    await Promise.all(promises);
    return performance.now() - startTime;
}

async function runPerformanceTest(fileCount: number): Promise<PerformanceTestResult> {
    console.log(`\n🧪 Testing with ${fileCount} files...`);
    
    const files = await getTestFiles(fileCount);
    console.log(`📁 Selected ${files.length} files for testing`);
    
    // Test worker pool performance
    console.log(`🚀 Testing worker pool performance...`);
    const workerPoolTime = await testWorkerPoolPerformance(files);
    console.log(`⚡ Worker pool completed in ${workerPoolTime.toFixed(2)}ms`);
    
    // Test direct analysis performance
    console.log(`🔧 Testing direct analysis performance...`);
    const directAnalysisTime = await testDirectAnalysisPerformance(files);
    console.log(`⚡ Direct analysis completed in ${directAnalysisTime.toFixed(2)}ms`);
    
    const speedupFactor = directAnalysisTime / workerPoolTime;
    
    return {
        fileCount: files.length,
        workerPoolTime,
        directAnalysisTime,
        speedupFactor,
        avgTimePerFile: {
            workerPool: workerPoolTime / files.length,
            directAnalysis: directAnalysisTime / files.length
        }
    };
}

async function main() {
    console.log('🎯 Parallelization Performance Test');
    console.log('===================================');
    
    const testCounts = [5, 10, 25];
    const results: PerformanceTestResult[] = [];
    
    for (const count of testCounts) {
        try {
            const result = await runPerformanceTest(count);
            results.push(result);
            
            console.log(`\n📊 Results for ${result.fileCount} files:`);
            console.log(`   Worker Pool: ${result.workerPoolTime.toFixed(2)}ms (${result.avgTimePerFile.workerPool.toFixed(2)}ms/file)`);
            console.log(`   Direct Analysis: ${result.directAnalysisTime.toFixed(2)}ms (${result.avgTimePerFile.directAnalysis.toFixed(2)}ms/file)`);
            console.log(`   Speedup Factor: ${result.speedupFactor.toFixed(2)}x`);
            
        } catch (error) {
            console.error(`❌ Error testing ${count} files:`, error);
        }
    }
    
    console.log('\n🏆 Performance Summary:');
    console.log('========================');
    
    results.forEach(result => {
        const status = result.speedupFactor > 1 ? '✅ FASTER' : '❌ SLOWER';
        console.log(`${result.fileCount} files: ${result.speedupFactor.toFixed(2)}x speedup ${status}`);
    });
    
    const avgSpeedup = results.reduce((sum, r) => sum + r.speedupFactor, 0) / results.length;
    console.log(`\n🎯 Average Speedup: ${avgSpeedup.toFixed(2)}x`);
    
    if (avgSpeedup > 1) {
        console.log('🎉 TRUE PARALLELIZATION IS WORKING!');
    } else {
        console.log('⚠️ Parallelization needs optimization');
    }
}

if (require.main === module) {
    main().catch(console.error);
}
