/**
 * Comprehensive Error Analysis Service for Simonitor Auto-Scan
 * Captures, categorizes, and analyzes all errors during mod scanning
 */

import * as fs from 'fs';
import * as path from 'path';

export interface ErrorEntry {
    timestamp: Date;
    errorType: string;
    category: ErrorCategory;
    severity: ErrorSeverity;
    fileName?: string;
    message: string;
    stackTrace?: string;
    context?: Record<string, any>;
    frequency: number;
}

export enum ErrorCategory {
    THUMBNAIL_EXTRACTION = 'thumbnail_extraction',
    PACKAGE_ANALYSIS = 'package_analysis',
    S4TK_PROCESSING = 's4tk_processing',
    DXT_DECOMPRESSION = 'dxt_decompression',
    BUFFER_VALIDATION = 'buffer_validation',
    FILE_IO = 'file_io',
    MEMORY = 'memory',
    TIMEOUT = 'timeout',
    UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
    CRITICAL = 'critical',    // App-breaking errors
    HIGH = 'high',           // Performance-impacting errors
    MEDIUM = 'medium',       // Functionality-affecting errors
    LOW = 'low'              // Cosmetic/minor errors
}

export class ErrorAnalysisService {
    private static instance: ErrorAnalysisService;
    private errors: Map<string, ErrorEntry> = new Map();
    private logFilePath: string;
    private isLogging: boolean = false;

    private constructor() {
        // Create logs directory if it doesn't exist
        const logsDir = path.join(process.cwd(), 'logs');
        if (!fs.existsSync(logsDir)) {
            fs.mkdirSync(logsDir, { recursive: true });
        }
        
        this.logFilePath = path.join(logsDir, `error-analysis-${Date.now()}.json`);
    }

    public static getInstance(): ErrorAnalysisService {
        if (!ErrorAnalysisService.instance) {
            ErrorAnalysisService.instance = new ErrorAnalysisService();
        }
        return ErrorAnalysisService.instance;
    }

    /**
     * Start error logging session
     */
    public startLogging(): void {
        this.isLogging = true;
        this.errors.clear();
        console.log(`🔍 [ErrorAnalysis] Started comprehensive error logging session`);
        console.log(`📝 [ErrorAnalysis] Log file: ${this.logFilePath}`);
    }

    /**
     * Stop error logging session and save results
     */
    public stopLogging(): ErrorAnalysisReport {
        this.isLogging = false;
        const report = this.generateReport();
        this.saveToFile(report);
        console.log(`✅ [ErrorAnalysis] Completed error logging session`);
        console.log(`📊 [ErrorAnalysis] Total unique errors: ${this.errors.size}`);
        return report;
    }

    /**
     * Log an error with automatic categorization
     */
    public logError(
        error: Error | string,
        context: {
            fileName?: string;
            operation?: string;
            additionalData?: Record<string, any>;
        } = {}
    ): void {
        if (!this.isLogging) return;

        const errorMessage = error instanceof Error ? error.message : error;
        const stackTrace = error instanceof Error ? error.stack : undefined;
        
        // Generate unique key for error deduplication
        const errorKey = this.generateErrorKey(errorMessage, context.fileName, context.operation);
        
        const existingError = this.errors.get(errorKey);
        if (existingError) {
            existingError.frequency++;
            existingError.timestamp = new Date();
            return;
        }

        // Categorize and assess severity
        const category = this.categorizeError(errorMessage, stackTrace);
        const severity = this.assessSeverity(errorMessage, category);

        const errorEntry: ErrorEntry = {
            timestamp: new Date(),
            errorType: this.extractErrorType(errorMessage),
            category,
            severity,
            fileName: context.fileName,
            message: errorMessage,
            stackTrace,
            context: {
                operation: context.operation,
                ...context.additionalData
            },
            frequency: 1
        };

        this.errors.set(errorKey, errorEntry);

        // Log to console with appropriate level
        const logLevel = severity === ErrorSeverity.CRITICAL ? 'error' : 
                        severity === ErrorSeverity.HIGH ? 'warn' : 'log';
        console[logLevel](`🚨 [ErrorAnalysis] ${severity.toUpperCase()}: ${errorMessage}`);
    }

    /**
     * Generate unique key for error deduplication
     */
    private generateErrorKey(message: string, fileName?: string, operation?: string): string {
        const normalizedMessage = message.replace(/\d+/g, 'N').replace(/0x[a-fA-F0-9]+/g, '0xHEX');
        return `${normalizedMessage}|${fileName || 'unknown'}|${operation || 'unknown'}`;
    }

    /**
     * Categorize error based on message and stack trace
     */
    private categorizeError(message: string, stackTrace?: string): ErrorCategory {
        const lowerMessage = message.toLowerCase();
        const lowerStack = stackTrace?.toLowerCase() || '';

        // S4TK Processing errors
        if (lowerMessage.includes('s4tk') || lowerStack.includes('s4tk') ||
            lowerMessage.includes('ddsimage') || lowerMessage.includes('ddsimageres')) {
            return ErrorCategory.S4TK_PROCESSING;
        }

        // DXT Decompression errors
        if (lowerMessage.includes('dxt') || lowerMessage.includes('offset is out of bounds') ||
            lowerMessage.includes('pointerFromData') || lowerMessage.includes('decompress')) {
            return ErrorCategory.DXT_DECOMPRESSION;
        }

        // Thumbnail extraction errors
        if (lowerMessage.includes('thumbnail') || lowerMessage.includes('jimp') ||
            lowerMessage.includes('image') || lowerStack.includes('thumbnailextraction')) {
            return ErrorCategory.THUMBNAIL_EXTRACTION;
        }

        // Buffer validation errors
        if (lowerMessage.includes('buffer') || lowerMessage.includes('validation') ||
            lowerMessage.includes('invalid') && lowerMessage.includes('size')) {
            return ErrorCategory.BUFFER_VALIDATION;
        }

        // Package analysis errors
        if (lowerMessage.includes('package') || lowerMessage.includes('analysis') ||
            lowerStack.includes('packageparser') || lowerStack.includes('contentanalysis')) {
            return ErrorCategory.PACKAGE_ANALYSIS;
        }

        // File I/O errors
        if (lowerMessage.includes('enoent') || lowerMessage.includes('file') ||
            lowerMessage.includes('read') || lowerMessage.includes('access')) {
            return ErrorCategory.FILE_IO;
        }

        // Memory errors
        if (lowerMessage.includes('memory') || lowerMessage.includes('heap') ||
            lowerMessage.includes('allocation')) {
            return ErrorCategory.MEMORY;
        }

        // Timeout errors
        if (lowerMessage.includes('timeout') || lowerMessage.includes('abort')) {
            return ErrorCategory.TIMEOUT;
        }

        return ErrorCategory.UNKNOWN;
    }

    /**
     * Assess error severity
     */
    private assessSeverity(message: string, category: ErrorCategory): ErrorSeverity {
        const lowerMessage = message.toLowerCase();

        // Critical errors that can crash the app
        if (lowerMessage.includes('crash') || lowerMessage.includes('fatal') ||
            lowerMessage.includes('segmentation') || lowerMessage.includes('heap overflow')) {
            return ErrorSeverity.CRITICAL;
        }

        // High severity for performance-impacting errors
        if (category === ErrorCategory.DXT_DECOMPRESSION ||
            lowerMessage.includes('freeze') || lowerMessage.includes('hang') ||
            lowerMessage.includes('timeout') || lowerMessage.includes('memory')) {
            return ErrorSeverity.HIGH;
        }

        // Medium severity for functionality-affecting errors
        if (category === ErrorCategory.S4TK_PROCESSING ||
            category === ErrorCategory.THUMBNAIL_EXTRACTION ||
            category === ErrorCategory.PACKAGE_ANALYSIS) {
            return ErrorSeverity.MEDIUM;
        }

        // Low severity for minor issues
        return ErrorSeverity.LOW;
    }

    /**
     * Extract error type from message
     */
    private extractErrorType(message: string): string {
        // Common error patterns
        const patterns = [
            /^(\w+Error):/,
            /^(\w+Exception):/,
            /^(Failed to .+?):/,
            /^(Could not .+?):/,
            /^(Unable to .+?):/,
            /^(Invalid .+?):/
        ];

        for (const pattern of patterns) {
            const match = message.match(pattern);
            if (match) {
                return match[1];
            }
        }

        // Fallback to first few words
        return message.split(' ').slice(0, 3).join(' ');
    }

    /**
     * Generate comprehensive error report
     */
    private generateReport(): ErrorAnalysisReport {
        const errors = Array.from(this.errors.values());
        
        return {
            sessionInfo: {
                startTime: new Date(),
                totalErrors: errors.length,
                totalOccurrences: errors.reduce((sum, e) => sum + e.frequency, 0)
            },
            errorsByCategory: this.groupByCategory(errors),
            errorsBySeverity: this.groupBySeverity(errors),
            topErrors: errors.sort((a, b) => b.frequency - a.frequency).slice(0, 20),
            recommendations: this.generateRecommendations(errors)
        };
    }

    private groupByCategory(errors: ErrorEntry[]): Record<string, ErrorEntry[]> {
        const grouped: Record<string, ErrorEntry[]> = {};
        for (const error of errors) {
            if (!grouped[error.category]) {
                grouped[error.category] = [];
            }
            grouped[error.category].push(error);
        }
        return grouped;
    }

    private groupBySeverity(errors: ErrorEntry[]): Record<string, ErrorEntry[]> {
        const grouped: Record<string, ErrorEntry[]> = {};
        for (const error of errors) {
            if (!grouped[error.severity]) {
                grouped[error.severity] = [];
            }
            grouped[error.severity].push(error);
        }
        return grouped;
    }

    private generateRecommendations(errors: ErrorEntry[]): string[] {
        const recommendations: string[] = [];
        const categories = this.groupByCategory(errors);

        if (categories[ErrorCategory.DXT_DECOMPRESSION]?.length > 0) {
            recommendations.push('Implement enhanced DXT buffer validation and size checking');
        }

        if (categories[ErrorCategory.S4TK_PROCESSING]?.length > 0) {
            recommendations.push('Add S4TK error boundaries and fallback processing');
        }

        if (categories[ErrorCategory.THUMBNAIL_EXTRACTION]?.length > 0) {
            recommendations.push('Improve thumbnail extraction error handling and fallbacks');
        }

        return recommendations;
    }

    private saveToFile(report: ErrorAnalysisReport): void {
        try {
            fs.writeFileSync(this.logFilePath, JSON.stringify(report, null, 2));
            console.log(`💾 [ErrorAnalysis] Report saved to: ${this.logFilePath}`);
        } catch (error) {
            console.error('Failed to save error analysis report:', error);
        }
    }
}

export interface ErrorAnalysisReport {
    sessionInfo: {
        startTime: Date;
        totalErrors: number;
        totalOccurrences: number;
    };
    errorsByCategory: Record<string, ErrorEntry[]>;
    errorsBySeverity: Record<string, ErrorEntry[]>;
    topErrors: ErrorEntry[];
    recommendations: string[];
}
