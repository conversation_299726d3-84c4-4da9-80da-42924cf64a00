/**
 * Isolated Worker Pool
 * Uses the isolated analysis workers that don't import Electron APIs
 */

import { cpus } from 'os';
import { IsolatedAnalysisWorkerClient } from './IsolatedAnalysisWorker';

export interface IsolatedWorkerPoolConfig {
  maxWorkers: number;
  maxQueueSize: number;
  workerTimeout: number;
  enableHealthChecks: boolean;
  healthCheckInterval: number;
}

export interface IsolatedPoolStats {
  totalWorkers: number;
  activeWorkers: number;
  idleWorkers: number;
  queuedTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageTaskTime: number;
}

export interface IsolatedAnalysisTask {
  id: string;
  buffer: Buffer;
  filePath: string;
  resolve: (result: any) => void;
  reject: (error: Error) => void;
  startTime?: number;
}

export class IsolatedWorkerPool {
  private workers: IsolatedAnalysisWorkerClient[] = [];
  private availableWorkers: IsolatedAnalysisWorkerClient[] = [];
  private taskQueue: IsolatedAnalysisTask[] = [];
  private activeTasks = new Map<IsolatedAnalysisWorkerClient, IsolatedAnalysisTask>();
  private config: IsolatedWorkerPoolConfig;
  private stats: IsolatedPoolStats;
  private healthCheckTimer?: NodeJS.Timeout;
  private isShuttingDown = false;

  constructor(config: Partial<IsolatedWorkerPoolConfig> = {}) {
    this.config = {
      maxWorkers: config.maxWorkers || Math.max(1, cpus().length - 1),
      maxQueueSize: config.maxQueueSize || 1000,
      workerTimeout: config.workerTimeout || 30000, // 30 seconds
      enableHealthChecks: config.enableHealthChecks ?? true,
      healthCheckInterval: config.healthCheckInterval || 60000, // 1 minute
      ...config
    };

    this.stats = {
      totalWorkers: 0,
      activeWorkers: 0,
      idleWorkers: 0,
      queuedTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      averageTaskTime: 0
    };

    this.initialize();
  }

  private async initialize(): Promise<void> {
    console.log(`🚀 [IsolatedWorkerPool] Initializing with ${this.config.maxWorkers} workers`);
    
    // Create initial workers
    for (let i = 0; i < this.config.maxWorkers; i++) {
      await this.createWorker();
    }

    // Start health checks
    if (this.config.enableHealthChecks) {
      this.startHealthChecks();
    }

    console.log(`✅ [IsolatedWorkerPool] Initialized with ${this.workers.length} workers`);
  }

  private async createWorker(): Promise<IsolatedAnalysisWorkerClient> {
    try {
      const worker = new IsolatedAnalysisWorkerClient();
      
      // Test worker health
      const isHealthy = await worker.ping();
      if (!isHealthy) {
        throw new Error('Worker failed health check');
      }

      this.workers.push(worker);
      this.availableWorkers.push(worker);
      this.stats.totalWorkers++;
      this.stats.idleWorkers++;

      console.log(`✅ [IsolatedWorkerPool] Created worker ${this.workers.length}/${this.config.maxWorkers}`);
      return worker;
    } catch (error) {
      console.error('❌ [IsolatedWorkerPool] Failed to create worker:', error);
      throw error;
    }
  }

  private async removeWorker(worker: IsolatedAnalysisWorkerClient): Promise<void> {
    try {
      // Cancel any active task
      const activeTask = this.activeTasks.get(worker);
      if (activeTask) {
        activeTask.reject(new Error('Worker terminated'));
        this.activeTasks.delete(worker);
        this.stats.activeWorkers--;
        this.stats.failedTasks++;
      }

      // Remove from available workers
      const availableIndex = this.availableWorkers.indexOf(worker);
      if (availableIndex !== -1) {
        this.availableWorkers.splice(availableIndex, 1);
        this.stats.idleWorkers--;
      }

      // Remove from workers array
      const workerIndex = this.workers.indexOf(worker);
      if (workerIndex !== -1) {
        this.workers.splice(workerIndex, 1);
        this.stats.totalWorkers--;
      }

      // Terminate worker
      await worker.terminate();
      console.log(`🗑️ [IsolatedWorkerPool] Removed worker (${this.workers.length} remaining)`);
    } catch (error) {
      console.error('❌ [IsolatedWorkerPool] Error removing worker:', error);
    }
  }

  async analyze(buffer: Buffer, filePath: string): Promise<any> {
    if (this.isShuttingDown) {
      throw new Error('Worker pool is shutting down');
    }

    if (this.taskQueue.length >= this.config.maxQueueSize) {
      throw new Error('Task queue is full');
    }

    return new Promise((resolve, reject) => {
      const task: IsolatedAnalysisTask = {
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        buffer,
        filePath,
        resolve,
        reject
      };

      this.taskQueue.push(task);
      this.stats.queuedTasks++;
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    while (this.taskQueue.length > 0 && this.availableWorkers.length > 0) {
      const task = this.taskQueue.shift()!;
      const worker = this.availableWorkers.shift()!;

      this.stats.queuedTasks--;
      this.stats.idleWorkers--;
      this.stats.activeWorkers++;

      task.startTime = Date.now();
      this.activeTasks.set(worker, task);

      // Process task
      this.processTask(worker, task);
    }
  }

  private async processTask(worker: IsolatedAnalysisWorkerClient, task: IsolatedAnalysisTask): Promise<void> {
    try {
      const result = await worker.analyze(task.buffer, task.filePath);
      
      // Calculate task time
      const taskTime = task.startTime ? Date.now() - task.startTime : 0;
      this.updateAverageTaskTime(taskTime);

      task.resolve(result);
      this.stats.completedTasks++;
    } catch (error) {
      task.reject(error instanceof Error ? error : new Error(String(error)));
      this.stats.failedTasks++;
    } finally {
      // Return worker to available pool
      this.activeTasks.delete(worker);
      this.availableWorkers.push(worker);
      this.stats.activeWorkers--;
      this.stats.idleWorkers++;

      // Process next task if any
      this.processQueue();
    }
  }

  private updateAverageTaskTime(taskTime: number): void {
    const totalTasks = this.stats.completedTasks + this.stats.failedTasks;
    if (totalTasks === 1) {
      this.stats.averageTaskTime = taskTime;
    } else {
      this.stats.averageTaskTime = (this.stats.averageTaskTime * (totalTasks - 1) + taskTime) / totalTasks;
    }
  }

  private startHealthChecks(): void {
    this.healthCheckTimer = setInterval(async () => {
      await this.performHealthChecks();
    }, this.config.healthCheckInterval);
  }

  private async performHealthChecks(): Promise<void> {
    console.log(`🔍 [IsolatedWorkerPool] Performing health checks on ${this.workers.length} workers`);
    
    const healthPromises = this.workers.map(async (worker) => {
      try {
        const isHealthy = await worker.ping();
        if (!isHealthy) {
          console.warn('⚠️ [IsolatedWorkerPool] Worker failed health check, removing...');
          await this.removeWorker(worker);
          // Create replacement worker
          await this.createWorker();
        }
      } catch (error) {
        console.error('❌ [IsolatedWorkerPool] Health check error:', error);
        await this.removeWorker(worker);
        await this.createWorker();
      }
    });

    await Promise.allSettled(healthPromises);
  }

  getStats(): IsolatedPoolStats {
    return { ...this.stats };
  }

  async shutdown(): Promise<void> {
    console.log('🛑 [IsolatedWorkerPool] Shutting down...');
    this.isShuttingDown = true;

    // Stop health checks
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    // Reject all queued tasks
    for (const task of this.taskQueue) {
      task.reject(new Error('Worker pool shutting down'));
    }
    this.taskQueue.length = 0;

    // Wait for active tasks to complete or timeout
    const activeTaskPromises = Array.from(this.activeTasks.values()).map(task => 
      new Promise<void>((resolve) => {
        const originalResolve = task.resolve;
        const originalReject = task.reject;
        
        task.resolve = (result) => {
          originalResolve(result);
          resolve();
        };
        
        task.reject = (error) => {
          originalReject(error);
          resolve();
        };

        // Timeout after 10 seconds
        setTimeout(resolve, 10000);
      })
    );

    await Promise.allSettled(activeTaskPromises);

    // Terminate all workers
    const terminatePromises = this.workers.map(worker => worker.terminate());
    await Promise.allSettled(terminatePromises);

    this.workers.length = 0;
    this.availableWorkers.length = 0;
    this.activeTasks.clear();

    console.log('✅ [IsolatedWorkerPool] Shutdown complete');
  }
}
