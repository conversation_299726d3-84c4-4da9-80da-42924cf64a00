/**
 * Test DDS Library Loading
 * Verifies that the enhanced DDS processing libraries are loaded correctly
 */

console.log('🧪 Testing DDS Library Loading');
console.log('=' .repeat(50));

// Test Node.js environment detection
const isNodeEnvironment = typeof window === 'undefined' && typeof require !== 'undefined';
console.log(`🔍 Node.js environment detected: ${isNodeEnvironment}`);

if (isNodeEnvironment) {
    console.log('📦 Attempting to load DDS processing libraries...');
    
    try {
        const decodeDXT = require('decode-dxt');
        console.log('✅ decode-dxt loaded successfully');
        console.log(`   Type: ${typeof decodeDXT}`);
        console.log(`   Methods: ${Object.keys(decodeDXT)}`);
    } catch (error) {
        console.log('❌ Failed to load decode-dxt:', error);
    }

    try {
        const parseDDS = require('parse-dds');
        console.log('✅ parse-dds loaded successfully');
        console.log(`   Type: ${typeof parseDDS}`);
    } catch (error) {
        console.log('❌ Failed to load parse-dds:', error);
    }

    try {
        const ddsParser = require('dds-parser');
        console.log('✅ dds-parser loaded successfully');
        console.log(`   Type: ${typeof ddsParser}`);
        console.log(`   Methods: ${Object.keys(ddsParser)}`);
        
        // Test the methods we need
        if (ddsParser.parseHeaders) {
            console.log('   ✅ parseHeaders method available');
        } else {
            console.log('   ❌ parseHeaders method not found');
        }
        
        if (ddsParser.decodeDds) {
            console.log('   ✅ decodeDds method available');
        } else {
            console.log('   ❌ decodeDds method not found');
        }
    } catch (error) {
        console.log('❌ Failed to load dds-parser:', error);
    }

    // Test importing ThumbnailExtractionService
    try {
        console.log('\n📦 Testing ThumbnailExtractionService import...');
        const { ThumbnailExtractionService } = require('../services/visual/ThumbnailExtractionService');
        console.log('✅ ThumbnailExtractionService imported successfully');
        console.log(`   Type: ${typeof ThumbnailExtractionService}`);
        console.log(`   Methods: ${Object.getOwnPropertyNames(ThumbnailExtractionService).filter(name => typeof ThumbnailExtractionService[name] === 'function')}`);
    } catch (error) {
        console.log('❌ Failed to import ThumbnailExtractionService:', error);
    }

} else {
    console.log('⚠️ Not in Node.js environment - DDS libraries will not be available');
}

console.log('\n🏁 Library loading test completed!');
