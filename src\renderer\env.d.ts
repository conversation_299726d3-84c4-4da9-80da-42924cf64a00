/// <reference types="vite/client" />

export { };

declare global {
  interface Window {
    electronAPI: {
      analyzePackage: (filePath: string) => Promise<any>;
      analyzeModsFolder: (folderPath: string) => Promise<any>;
      analyzeModsFolderBackground: (folderPath: string, options?: any) => Promise<any>;
      getDefaultModsFolder: () => Promise<any>;
      selectModsFolder: () => Promise<any>;
      exportResults: (data: any, format: 'json' | 'csv') => Promise<any>;
      extractThumbnails: (filePath: string, options?: any) => Promise<any>;
      onAnalysisResult: (callback: (result: any) => void) => void;
      offAnalysisResult: (callback: (result: any) => void) => void;
      onScanProgress: (callback: (progress: any) => void) => void;
      offScanProgress: (callback: (progress: any) => void) => void;
    };
  }
}