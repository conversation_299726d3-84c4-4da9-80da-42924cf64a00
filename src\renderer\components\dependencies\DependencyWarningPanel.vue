<template>
  <div class="dependency-warning-panel">
    <!-- Header -->
    <div class="panel-header">
      <div class="header-content">
        <ExclamationTriangleIcon class="header-icon" />
        <div>
          <h3 class="panel-title">Dependency Issues Detected</h3>
          <p class="panel-subtitle">{{ totalIssues }} issues found across {{ affectedMods }} mods</p>
        </div>
      </div>
      <button @click="$emit('close')" class="close-button">
        <XMarkIcon class="w-5 h-5" />
      </button>
    </div>

    <!-- Summary Cards -->
    <div class="summary-cards">
      <div class="summary-card critical">
        <div class="card-icon">
          <XCircleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ criticalIssues }}</div>
          <div class="card-label">Critical Missing</div>
        </div>
      </div>
      
      <div class="summary-card warning">
        <div class="card-icon">
          <ExclamationTriangleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ warningIssues }}</div>
          <div class="card-label">Warnings</div>
        </div>
      </div>
      
      <div class="summary-card info">
        <div class="card-icon">
          <InformationCircleIcon class="w-6 h-6" />
        </div>
        <div class="card-content">
          <div class="card-number">{{ recommendationCount }}</div>
          <div class="card-label">Recommendations</div>
        </div>
      </div>
    </div>

    <!-- Missing Core Mods Section -->
    <div v-if="missingCoreMods.length > 0" class="section">
      <h4 class="section-title">
        <CogIcon class="section-icon" />
        Missing Core Mods
      </h4>
      <div class="core-mods-list">
        <div 
          v-for="coreMod in missingCoreMods" 
          :key="coreMod.id" 
          class="core-mod-card"
        >
          <div class="core-mod-header">
            <div class="core-mod-info">
              <h5 class="core-mod-name">{{ coreMod.name }}</h5>
              <p class="core-mod-author">by {{ coreMod.author }}</p>
            </div>
            <div class="core-mod-actions">
              <button 
                v-if="coreMod.downloadUrl" 
                @click="openDownloadUrl(coreMod.downloadUrl)"
                class="action-btn primary"
              >
                <ArrowDownTrayIcon class="w-4 h-4" />
                Download
              </button>
            </div>
          </div>
          
          <p class="core-mod-description">{{ coreMod.description }}</p>
          
          <div class="dependent-mods">
            <span class="dependent-label">Affects {{ getDependentModCount(coreMod.id) }} mods:</span>
            <div class="dependent-list">
              <span 
                v-for="mod in getDependentMods(coreMod.id).slice(0, 3)" 
                :key="mod" 
                class="dependent-mod"
              >
                {{ mod }}
              </span>
              <span 
                v-if="getDependentMods(coreMod.id).length > 3" 
                class="dependent-more"
              >
                +{{ getDependentMods(coreMod.id).length - 3 }} more
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Dependency Warnings Section -->
    <div v-if="dependencyWarnings.length > 0" class="section">
      <h4 class="section-title">
        <ExclamationTriangleIcon class="section-icon" />
        Dependency Warnings
      </h4>
      <div class="warnings-list">
        <div 
          v-for="warning in dependencyWarnings" 
          :key="warning.type + warning.message" 
          :class="['warning-item', warning.severity]"
        >
          <div class="warning-header">
            <component :is="getWarningIcon(warning.type)" class="warning-icon" />
            <div class="warning-content">
              <h5 class="warning-title">{{ getWarningTitle(warning.type) }}</h5>
              <p class="warning-message">{{ warning.message }}</p>
            </div>
          </div>
          
          <div v-if="warning.affectedMods.length > 0" class="affected-mods">
            <span class="affected-label">Affected mods:</span>
            <div class="affected-list">
              <span 
                v-for="mod in warning.affectedMods.slice(0, 2)" 
                :key="mod" 
                class="affected-mod"
              >
                {{ mod }}
              </span>
              <span 
                v-if="warning.affectedMods.length > 2" 
                class="affected-more"
              >
                +{{ warning.affectedMods.length - 2 }} more
              </span>
            </div>
          </div>
          
          <div v-if="warning.resolution" class="warning-resolution">
            <span class="resolution-label">Resolution:</span>
            <p class="resolution-text">{{ warning.resolution }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Recommendations Section -->
    <div v-if="recommendations.length > 0" class="section">
      <h4 class="section-title">
        <LightBulbIcon class="section-icon" />
        Recommendations
      </h4>
      <div class="recommendations-list">
        <div 
          v-for="recommendation in recommendations" 
          :key="recommendation.description" 
          :class="['recommendation-item', recommendation.priority]"
        >
          <div class="recommendation-header">
            <component :is="getRecommendationIcon(recommendation.type)" class="recommendation-icon" />
            <div class="recommendation-content">
              <h5 class="recommendation-title">{{ recommendation.description }}</h5>
              <p class="recommendation-action">{{ recommendation.actionRequired }}</p>
            </div>
            <div class="recommendation-priority">
              <span :class="['priority-badge', recommendation.priority]">
                {{ recommendation.priority.toUpperCase() }}
              </span>
            </div>
          </div>
          
          <div class="recommendation-impact">
            <span class="impact-label">Impact:</span>
            <p class="impact-text">{{ recommendation.estimatedImpact }}</p>
          </div>
          
          <div v-if="recommendation.downloadUrl" class="recommendation-actions">
            <button 
              @click="openDownloadUrl(recommendation.downloadUrl)"
              class="action-btn secondary"
            >
              <ArrowTopRightOnSquareIcon class="w-4 h-4" />
              Open Link
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Actions Footer -->
    <div class="panel-footer">
      <button @click="refreshAnalysis" class="footer-btn secondary">
        <ArrowPathIcon class="w-4 h-4" />
        Refresh Analysis
      </button>
      <button @click="fixAllIssues" class="footer-btn primary" :disabled="!canAutoFix">
        <WrenchScrewdriverIcon class="w-4 h-4" />
        Auto-Fix Issues
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import {
  ExclamationTriangleIcon,
  XMarkIcon,
  XCircleIcon,
  InformationCircleIcon,
  CogIcon,
  ArrowDownTrayIcon,
  LightBulbIcon,
  ArrowTopRightOnSquareIcon,
  ArrowPathIcon,
  WrenchScrewdriverIcon,
  ClockIcon,
  ShieldExclamationIcon,
  DocumentTextIcon,
  PlusIcon,
  MinusIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline';

import type { 
  CoreModInfo, 
  DependencyWarning, 
  DependencyRecommendation,
  DependencyValidationResult 
} from '../../../services/analysis/dependencies/ComprehensiveDependencyService';

// Props
interface Props {
  validationResults: DependencyValidationResult[];
  missingCoreMods: CoreModInfo[];
  dependencyWarnings: DependencyWarning[];
  recommendations: DependencyRecommendation[];
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  refresh: [];
  autoFix: [];
}>();

// Computed properties
const totalIssues = computed(() => {
  return props.validationResults.reduce((total, result) => 
    total + result.missingDependencies.length + result.warnings.length, 0
  );
});

const affectedMods = computed(() => {
  return props.validationResults.filter(result => 
    !result.isValid || result.warnings.length > 0
  ).length;
});

const criticalIssues = computed(() => {
  return props.validationResults.reduce((total, result) => 
    total + result.missingDependencies.filter(d => d.severity === 'critical').length, 0
  );
});

const warningIssues = computed(() => {
  return props.dependencyWarnings.filter(w => w.severity === 'high' || w.severity === 'medium').length;
});

const recommendationCount = computed(() => {
  return props.recommendations.length;
});

const canAutoFix = computed(() => {
  return props.recommendations.some(r => r.type === 'install' && r.downloadUrl);
});

// Methods
const openDownloadUrl = (url: string) => {
  window.electronAPI?.openExternal?.(url);
};

const getDependentModCount = (coreModId: string): number => {
  return props.validationResults.filter(result => 
    result.dependencies.requiredMods.some(mod => 
      props.missingCoreMods.find(cm => cm.id === coreModId)?.name === mod
    )
  ).length;
};

const getDependentMods = (coreModId: string): string[] => {
  return props.validationResults
    .filter(result => 
      result.dependencies.requiredMods.some(mod => 
        props.missingCoreMods.find(cm => cm.id === coreModId)?.name === mod
      )
    )
    .map(result => result.modFileName);
};

const getWarningIcon = (type: string) => {
  switch (type) {
    case 'version_mismatch': return ClockIcon;
    case 'outdated_dependency': return ClockIcon;
    case 'potential_conflict': return ShieldExclamationIcon;
    case 'deprecated': return DocumentTextIcon;
    default: return ExclamationTriangleIcon;
  }
};

const getWarningTitle = (type: string): string => {
  switch (type) {
    case 'version_mismatch': return 'Version Mismatch';
    case 'outdated_dependency': return 'Outdated Dependency';
    case 'potential_conflict': return 'Potential Conflict';
    case 'deprecated': return 'Deprecated Dependency';
    default: return 'Warning';
  }
};

const getRecommendationIcon = (type: string) => {
  switch (type) {
    case 'install': return PlusIcon;
    case 'update': return ArrowPathIcon;
    case 'remove': return MinusIcon;
    case 'configure': return Cog6ToothIcon;
    default: return LightBulbIcon;
  }
};

const refreshAnalysis = () => {
  emit('refresh');
};

const fixAllIssues = () => {
  emit('autoFix');
};
</script>

<style scoped>
/* Import design system */
@import '../../styles/simonitor-design-system.css';

.dependency-warning-panel {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  max-width: 896px; /* 4xl equivalent */
  width: 100%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.header-icon {
  width: 32px;
  height: 32px;
  color: var(--warning);
}

.panel-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.panel-subtitle {
  color: var(--text-secondary);
}

.close-button {
  padding: var(--space-2);
  color: var(--text-muted);
  background: transparent;
  border: none;
  border-radius: var(--radius-lg);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
}

.close-button:hover {
  color: var(--text-primary);
  background: var(--bg-secondary);
}

/* Summary Cards */
.summary-cards {
  @apply grid grid-cols-3 gap-4 p-6 border-b border-gray-200 dark:border-gray-700;
}

.summary-card {
  @apply flex items-center space-x-3 p-4 rounded-lg;
}

.summary-card.critical {
  @apply bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800;
}

.summary-card.warning {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800;
}

.summary-card.info {
  @apply bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800;
}

.card-icon {
  @apply flex-shrink-0;
}

.summary-card.critical .card-icon {
  @apply text-red-600 dark:text-red-400;
}

.summary-card.warning .card-icon {
  @apply text-yellow-600 dark:text-yellow-400;
}

.summary-card.info .card-icon {
  @apply text-blue-600 dark:text-blue-400;
}

.card-number {
  @apply text-2xl font-bold text-gray-900 dark:text-white;
}

.card-label {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

/* Sections */
.section {
  @apply p-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0;
}

.section-title {
  @apply flex items-center space-x-2 text-lg font-semibold text-gray-900 dark:text-white mb-4;
}

.section-icon {
  @apply w-5 h-5 text-gray-500 dark:text-gray-400;
}

/* Core Mods */
.core-mods-list {
  @apply space-y-4;
}

.core-mod-card {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600;
}

.core-mod-header {
  @apply flex items-start justify-between mb-3;
}

.core-mod-name {
  @apply text-lg font-semibold text-gray-900 dark:text-white;
}

.core-mod-author {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.core-mod-description {
  @apply text-gray-700 dark:text-gray-300 mb-3;
}

.dependent-mods {
  @apply space-y-2;
}

.dependent-label {
  @apply text-sm font-medium text-gray-600 dark:text-gray-400;
}

.dependent-list {
  @apply flex flex-wrap gap-2;
}

.dependent-mod {
  @apply px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300
         text-xs rounded-full;
}

.dependent-more {
  @apply px-2 py-1 bg-gray-300 dark:bg-gray-500 text-gray-600 dark:text-gray-400
         text-xs rounded-full;
}

/* Warnings */
.warnings-list {
  @apply space-y-4;
}

.warning-item {
  @apply rounded-lg p-4 border;
}

.warning-item.high {
  @apply bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800;
}

.warning-item.medium {
  @apply bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800;
}

.warning-item.low {
  @apply bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800;
}

.warning-header {
  @apply flex items-start space-x-3 mb-3;
}

.warning-icon {
  @apply w-5 h-5 flex-shrink-0 mt-0.5;
}

.warning-item.high .warning-icon {
  @apply text-red-600 dark:text-red-400;
}

.warning-item.medium .warning-icon {
  @apply text-yellow-600 dark:text-yellow-400;
}

.warning-item.low .warning-icon {
  @apply text-blue-600 dark:text-blue-400;
}

.warning-title {
  @apply font-semibold text-gray-900 dark:text-white;
}

.warning-message {
  @apply text-gray-700 dark:text-gray-300;
}

/* Recommendations */
.recommendations-list {
  @apply space-y-4;
}

.recommendation-item {
  @apply bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600;
}

.recommendation-header {
  @apply flex items-start justify-between mb-3;
}

.recommendation-icon {
  @apply w-5 h-5 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5;
}

.recommendation-title {
  @apply font-semibold text-gray-900 dark:text-white;
}

.recommendation-action {
  @apply text-gray-700 dark:text-gray-300;
}

.priority-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;
}

.priority-badge.high {
  @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

.priority-badge.medium {
  @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
}

.priority-badge.low {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

/* Action Buttons */
.action-btn {
  @apply flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-colors;
}

.action-btn.primary {
  @apply bg-green-500 text-white hover:bg-green-600;
}

.action-btn.secondary {
  @apply bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300
         hover:bg-gray-300 dark:hover:bg-gray-500;
}

/* Footer */
.panel-footer {
  @apply flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700;
}

.footer-btn {
  @apply flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors;
}

.footer-btn.primary {
  @apply bg-green-500 text-white hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed;
}

.footer-btn.secondary {
  @apply bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300
         hover:bg-gray-300 dark:hover:bg-gray-600;
}

/* Scrollable content */
.section {
  @apply overflow-y-auto;
}
</style>
