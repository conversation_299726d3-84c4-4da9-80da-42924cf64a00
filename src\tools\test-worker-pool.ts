#!/usr/bin/env tsx

/**
 * Test script for the new ElectronWorkerPool implementation
 * This tests the worker pool with real mod files to validate performance improvements
 */

import * as path from 'path'
import * as fs from 'fs/promises'
import { OptimizedAnalysisEngine } from '../services/analysis/OptimizedAnalysisEngine'

async function testWorkerPool() {
    console.log('🧪 Testing OptimizedAnalysisEngine (Worker Pool will be tested in Electron)')
    console.log('=' .repeat(60))

    // Note: ElectronWorkerPool can only be tested in the actual Electron environment
    // due to electron-vite's ?modulePath import syntax. This test validates the
    // underlying OptimizedAnalysisEngine that powers the workers.

    console.log('📝 This test validates the analysis engine that will run in workers')

    try {
        // Test with assets folder
        const assetsPath = path.join(process.cwd(), 'assets')
        console.log(`📁 Scanning assets folder: ${assetsPath}`)

        const files = await fs.readdir(assetsPath)
        const packageFiles = files.filter(file => file.endsWith('.package'))
        
        console.log(`📦 Found ${packageFiles.length} package files`)

        if (packageFiles.length === 0) {
            console.log('⚠️  No package files found in assets folder')
            return
        }

        // Test with first 5 files for quick validation
        const testFiles = packageFiles.slice(0, Math.min(5, packageFiles.length))
        console.log(`🎯 Testing with ${testFiles.length} files`)

        const startTime = performance.now()
        const results = []

        // Process files using OptimizedAnalysisEngine (simulating worker behavior)
        for (const file of testFiles) {
            const filePath = path.join(assetsPath, file)
            console.log(`🚀 Processing: ${file}`)

            const resultPromise = (async () => {
                const buffer = await fs.readFile(filePath)
                return await OptimizedAnalysisEngine.analyze(buffer, filePath, {
                    enableStreaming: true,
                    enableParallelProcessing: true,
                    enableAggressiveCaching: true,
                    enableMemoryOptimization: true,
                    targetProcessingTime: 120
                })
            })()

            results.push(resultPromise)
        }

        // Wait for all results
        console.log('⏳ Waiting for all analyses to complete...')
        const analysisResults = await Promise.all(results)
        
        const totalTime = performance.now() - startTime
        const avgTime = totalTime / testFiles.length

        console.log('\n📈 Performance Results:')
        console.log(`   Total Time: ${totalTime.toFixed(2)}ms`)
        console.log(`   Average Time per File: ${avgTime.toFixed(2)}ms`)
        console.log(`   Files per Second: ${(1000 / avgTime).toFixed(2)}`)
        console.log(`   Projected Time for 1000 files: ${(avgTime * 1000 / 1000 / 60).toFixed(2)} minutes`)

        // Validate results
        console.log('\n✅ Analysis Results Summary:')
        analysisResults.forEach((result, index) => {
            console.log(`   ${testFiles[index]}: ${result ? 'SUCCESS' : 'FAILED'}`)
            if (result) {
                console.log(`     - Resources: ${result.resources?.length || 0}`)
                console.log(`     - Quality Score: ${result.qualityAssessment?.overallScore || 'N/A'}`)
            }
        })

        console.log('\n🎯 Performance Target Analysis:')
        console.log(`   Current: ${avgTime.toFixed(2)}ms per file`)
        console.log(`   Target: 120ms per file (for 2-minute goal)`)
        console.log(`   Status: ${avgTime <= 120 ? '✅ MEETING TARGET' : '❌ NEEDS OPTIMIZATION'}`)

    } catch (error) {
        console.error('❌ Test failed:', error)
    } finally {
        console.log('\n✅ Test completed')
        console.log('📝 Note: Full worker pool testing requires running in Electron environment')
    }
}

// Run the test
if (require.main === module) {
    testWorkerPool().catch(console.error)
}

export { testWorkerPool }
