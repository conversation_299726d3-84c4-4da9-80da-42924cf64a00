/**
 * Minimal Worker for Testing
 * A completely minimal worker that only does basic file analysis
 * without any external dependencies to test the worker infrastructure
 */

import { Worker, isMainThread, parentPort } from 'worker_threads';
import * as crypto from 'crypto';

// Worker message types
export interface WorkerMessage {
  type: 'analyze' | 'cancel' | 'ping';
  id: string;
  data?: any;
}

export interface WorkerResponse {
  type: 'result' | 'error' | 'progress' | 'pong';
  id: string;
  data?: any;
  error?: string;
}

export interface AnalysisTask {
  id: string;
  buffer: Buffer;
  filePath: string;
}

// Minimal analysis result
interface MinimalAnalysisResult {
  filePath: string;
  fileSize: number;
  fileType: string;
  category: string;
  subcategory: string;
  resourceCount: number;
  isOverride: boolean;
  resources: any[];
  dependencies: any[];
  conflicts: any[];
  metadata: any;
  processingTime: number;
  hash: string;
}

// Worker implementation - only runs in worker thread
if (!isMainThread && parentPort) {
  console.log('🔧 [MinimalWorker] Worker thread starting...');
  
  const activeTasks = new Map<string, boolean>();

  // Handle messages from main thread
  parentPort.on('message', async (message: WorkerMessage) => {
    try {
      switch (message.type) {
        case 'analyze':
          await handleAnalysisTask(message);
          break;
        case 'cancel':
          handleCancellation(message);
          break;
        case 'ping':
          sendResponse({ type: 'pong', id: message.id });
          break;
        default:
          sendError(message.id, `Unknown message type: ${message.type}`);
      }
    } catch (error) {
      sendError(message.id, error instanceof Error ? error.message : String(error));
    }
  });

  async function handleAnalysisTask(message: WorkerMessage): Promise<void> {
    const task: AnalysisTask = message.data;
    activeTasks.set(task.id, false); // false = not cancelled
    
    try {
      const startTime = Date.now();
      
      // Check if cancelled
      if (activeTasks.get(task.id)) {
        return; // Task was cancelled
      }

      // Perform minimal analysis
      const result = performMinimalAnalysis(task.buffer, task.filePath);
      result.processingTime = Date.now() - startTime;

      // Send result if not cancelled
      if (!activeTasks.get(task.id)) {
        sendResponse({
          type: 'result',
          id: task.id,
          data: result
        });
      }
    } catch (error) {
      if (!activeTasks.get(task.id)) {
        sendError(task.id, error instanceof Error ? error.message : String(error));
      }
    } finally {
      activeTasks.delete(task.id);
    }
  }

  function handleCancellation(message: WorkerMessage): void {
    const taskId = message.data?.taskId;
    if (activeTasks.has(taskId)) {
      activeTasks.set(taskId, true); // Mark as cancelled
    }
  }

  function sendResponse(response: WorkerResponse): void {
    parentPort?.postMessage(response);
  }

  function sendError(id: string, error: string): void {
    sendResponse({
      type: 'error',
      id,
      error
    });
  }

  // Minimal analysis function - no external dependencies
  function performMinimalAnalysis(buffer: Buffer, filePath: string): MinimalAnalysisResult {
    const fileName = filePath.split(/[/\\]/).pop() || '';
    const ext = fileName.split('.').pop()?.toLowerCase();
    
    // Generate hash for caching
    const hash = crypto.createHash('md5').update(buffer).digest('hex');
    
    // Basic file type detection
    let fileType = 'unknown';
    let category = 'UNKNOWN';
    let subcategory = 'unknown';
    let resourceCount = 0;
    let isOverride = false;

    if (ext === 'ts4script') {
      fileType = 'script';
      category = 'SCRIPT';
      subcategory = 'script';
      resourceCount = 1;
    } else if (ext === 'package') {
      fileType = 'package';
      
      // Very basic package analysis - check for DBPF header
      if (buffer.length >= 4 && buffer.toString('ascii', 0, 4) === 'DBPF') {
        category = 'PACKAGE';
        subcategory = 'package';
        
        // Estimate resource count based on file size (very rough)
        resourceCount = Math.max(1, Math.floor(buffer.length / 1024));
        
        // Simple heuristic for override detection
        isOverride = fileName.toLowerCase().includes('override') || 
                    fileName.toLowerCase().includes('replace') ||
                    buffer.length < 50000; // Small files are often overrides
      } else {
        category = 'UNKNOWN';
        subcategory = 'invalid';
      }
    }

    return {
      filePath,
      fileSize: buffer.length,
      fileType,
      category,
      subcategory,
      resourceCount,
      isOverride,
      resources: [],
      dependencies: [],
      conflicts: [],
      metadata: { 
        fileName,
        fileType,
        hash,
        workerProcessed: true,
        analysisType: 'minimal'
      },
      processingTime: 0,
      hash
    };
  }

  // Signal worker is ready
  console.log('✅ [MinimalWorker] Worker thread initialized and ready');
  sendResponse({
    type: 'pong',
    id: 'worker-ready'
  });
}

// Export for main thread usage
export class MinimalWorkerClient {
  private worker: Worker;
  private pendingTasks = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
  }>();

  constructor() {
    this.worker = new Worker(__filename);
    this.setupMessageHandling();
  }

  private setupMessageHandling(): void {
    this.worker.on('message', (response: WorkerResponse) => {
      const pending = this.pendingTasks.get(response.id);
      
      if (pending) {
        if (response.type === 'result') {
          pending.resolve(response.data);
          this.pendingTasks.delete(response.id);
        } else if (response.type === 'error') {
          pending.reject(new Error(response.error || 'Worker error'));
          this.pendingTasks.delete(response.id);
        }
      }
    });

    this.worker.on('error', (error) => {
      console.error('❌ [MinimalWorker] Worker error:', error);
      // Reject all pending tasks
      for (const [id, pending] of this.pendingTasks) {
        pending.reject(error);
      }
      this.pendingTasks.clear();
    });
  }

  async analyze(buffer: Buffer, filePath: string): Promise<any> {
    const id = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      this.pendingTasks.set(id, { resolve, reject });
      
      this.worker.postMessage({
        type: 'analyze',
        id,
        data: { id, buffer, filePath }
      } as WorkerMessage);
    });
  }

  async ping(): Promise<boolean> {
    const id = `ping-${Date.now()}`;
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(id);
        resolve(false);
      }, 5000);

      this.pendingTasks.set(id, {
        resolve: () => {
          clearTimeout(timeout);
          resolve(true);
        },
        reject: () => {
          clearTimeout(timeout);
          resolve(false);
        }
      });

      this.worker.postMessage({
        type: 'ping',
        id
      } as WorkerMessage);
    });
  }

  terminate(): Promise<number> {
    return this.worker.terminate();
  }
}
