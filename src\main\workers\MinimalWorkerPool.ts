/**
 * Minimal Worker Pool for Testing
 * Tests the worker infrastructure with minimal dependencies
 */

import { cpus } from 'os';
import { MinimalWorkerClient } from './MinimalWorker';

export interface MinimalWorkerPoolConfig {
  maxWorkers: number;
  maxQueueSize: number;
  enableHealthChecks: boolean;
}

export class MinimalWorkerPool {
  private workers: MinimalWorkerClient[] = [];
  private availableWorkers: MinimalWorkerClient[] = [];
  private taskQueue: any[] = [];
  private activeTasks = new Map<MinimalWorkerClient, any>();
  private config: MinimalWorkerPoolConfig;
  private isShuttingDown = false;

  constructor(config: Partial<MinimalWorkerPoolConfig> = {}) {
    this.config = {
      maxWorkers: config.maxWorkers || Math.max(1, cpus().length - 1),
      maxQueueSize: config.maxQueueSize || 1000,
      enableHealthChecks: config.enableHealthChecks ?? true,
      ...config
    };

    this.initialize();
  }

  private async initialize(): Promise<void> {
    console.log(`🚀 [MinimalWorkerPool] Initializing with ${this.config.maxWorkers} workers`);
    
    // Create initial workers
    for (let i = 0; i < this.config.maxWorkers; i++) {
      await this.createWorker();
    }

    console.log(`✅ [MinimalWorkerPool] Initialized with ${this.workers.length} workers`);
  }

  private async createWorker(): Promise<MinimalWorkerClient> {
    try {
      const worker = new MinimalWorkerClient();
      
      // Test worker health
      const isHealthy = await worker.ping();
      if (!isHealthy) {
        throw new Error('Worker failed health check');
      }

      this.workers.push(worker);
      this.availableWorkers.push(worker);

      console.log(`✅ [MinimalWorkerPool] Created worker ${this.workers.length}/${this.config.maxWorkers}`);
      return worker;
    } catch (error) {
      console.error('❌ [MinimalWorkerPool] Failed to create worker:', error);
      throw error;
    }
  }

  async analyze(buffer: Buffer, filePath: string): Promise<any> {
    if (this.isShuttingDown) {
      throw new Error('Worker pool is shutting down');
    }

    if (this.taskQueue.length >= this.config.maxQueueSize) {
      throw new Error('Task queue is full');
    }

    return new Promise((resolve, reject) => {
      const task = {
        id: `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        buffer,
        filePath,
        resolve,
        reject
      };

      this.taskQueue.push(task);
      this.processQueue();
    });
  }

  private async processQueue(): Promise<void> {
    while (this.taskQueue.length > 0 && this.availableWorkers.length > 0) {
      const task = this.taskQueue.shift()!;
      const worker = this.availableWorkers.shift()!;

      this.activeTasks.set(worker, task);

      // Process task
      this.processTask(worker, task);
    }
  }

  private async processTask(worker: MinimalWorkerClient, task: any): Promise<void> {
    try {
      const result = await worker.analyze(task.buffer, task.filePath);
      task.resolve(result);
    } catch (error) {
      task.reject(error instanceof Error ? error : new Error(String(error)));
    } finally {
      // Return worker to available pool
      this.activeTasks.delete(worker);
      this.availableWorkers.push(worker);

      // Process next task if any
      this.processQueue();
    }
  }

  async shutdown(): Promise<void> {
    console.log('🛑 [MinimalWorkerPool] Shutting down...');
    this.isShuttingDown = true;

    // Reject all queued tasks
    for (const task of this.taskQueue) {
      task.reject(new Error('Worker pool shutting down'));
    }
    this.taskQueue.length = 0;

    // Terminate all workers
    const terminatePromises = this.workers.map(worker => worker.terminate());
    await Promise.allSettled(terminatePromises);

    this.workers.length = 0;
    this.availableWorkers.length = 0;
    this.activeTasks.clear();

    console.log('✅ [MinimalWorkerPool] Shutdown complete');
  }
}
