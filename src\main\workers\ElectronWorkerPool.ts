import { Worker } from 'worker_threads'
import { EventEmitter } from 'events'
import type { AnalysisRequest, AnalysisResult } from '../../types/analysis'

// Import worker using electron-vite's ?modulePath syntax
import workerPath from './OptimizedAnalysisWorker?modulePath'

interface WorkerTask {
  request: AnalysisRequest
  resolve: (result: AnalysisResult) => void
  reject: (error: Error) => void
  startTime: number
}

interface WorkerInstance {
  worker: Worker
  id: string
  busy: boolean
  currentTask?: WorkerTask
}

export class ElectronWorkerPool extends EventEmitter {
  private workers: WorkerInstance[] = []
  private taskQueue: WorkerTask[] = []
  private maxWorkers: number
  private activeWorkers = 0
  private totalProcessed = 0
  private totalProcessingTime = 0

  constructor(maxWorkers: number = 4) {
    super()
    this.maxWorkers = Math.min(maxWorkers, 8) // Cap at 8 workers for stability
    this.initializeWorkers()
  }

  private initializeWorkers(): void {
    for (let i = 0; i < this.maxWorkers; i++) {
      this.createWorker(i)
    }
  }

  private createWorker(id: number): void {
    const workerId = `worker-${id}`

    try {
      console.log(`🔧 Creating worker ${workerId} with path: ${workerPath}`)

      const worker = new Worker(workerPath, {
        workerData: { workerId }
      })

      const workerInstance: WorkerInstance = {
        worker,
        id: workerId,
        busy: false
      }

      worker.on('message', (message) => {
        if (message.type === 'ready') {
          console.log(`✅ Worker ${workerId} is ready`)
          this.emit('workerReady', workerId)
          return
        }

        this.handleWorkerMessage(workerInstance, message)
      })

      worker.on('error', (error) => {
        console.error(`❌ Worker ${workerId} error:`, error)
        this.handleWorkerError(workerInstance, error)
      })

      worker.on('exit', (code) => {
        if (code !== 0) {
          console.error(`❌ Worker ${workerId} exited with code ${code}`)
          this.recreateWorker(workerInstance)
        } else {
          console.log(`✅ Worker ${workerId} exited cleanly`)
        }
      })

      this.workers.push(workerInstance)
      this.activeWorkers++

      console.log(`✅ Worker ${workerId} created successfully`)

    } catch (error) {
      console.error(`❌ Failed to create worker ${workerId}:`, error)
      console.error(`❌ Worker path: ${workerPath}`)
      console.error(`❌ Error details:`, error)
      this.emit('error', error)
    }
  }

  private handleWorkerMessage(workerInstance: WorkerInstance, message: any): void {
    const task = workerInstance.currentTask
    if (!task) return

    workerInstance.busy = false
    workerInstance.currentTask = undefined

    this.totalProcessed++
    if (message.processingTime) {
      this.totalProcessingTime += message.processingTime
    }

    if (message.success) {
      task.resolve(message.result)
    } else {
      task.reject(new Error(message.error))
    }

    // Process next task in queue
    this.processNextTask()

    // Emit progress
    this.emit('progress', {
      processed: this.totalProcessed,
      queued: this.taskQueue.length,
      avgProcessingTime: this.totalProcessingTime / this.totalProcessed
    })
  }

  private handleWorkerError(workerInstance: WorkerInstance, error: Error): void {
    console.error(`Worker ${workerInstance.id} error:`, error)
    
    if (workerInstance.currentTask) {
      workerInstance.currentTask.reject(error)
      workerInstance.currentTask = undefined
    }

    workerInstance.busy = false
    this.recreateWorker(workerInstance)
  }

  private recreateWorker(workerInstance: WorkerInstance): void {
    const index = this.workers.indexOf(workerInstance)
    if (index !== -1) {
      this.workers.splice(index, 1)
      this.activeWorkers--
      
      // Create replacement worker
      setTimeout(() => {
        this.createWorker(Date.now() % 1000)
      }, 100)
    }
  }

  private processNextTask(): void {
    if (this.taskQueue.length === 0) return

    const availableWorker = this.workers.find(w => !w.busy)
    if (!availableWorker) return

    const task = this.taskQueue.shift()!
    availableWorker.busy = true
    availableWorker.currentTask = task

    availableWorker.worker.postMessage(task.request)
  }

  public async analyze(filePath: string, options: any = {}): Promise<AnalysisResult> {
    return new Promise((resolve, reject) => {
      const request: AnalysisRequest = {
        filePath,
        options,
        requestId: `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }

      const task: WorkerTask = {
        request,
        resolve,
        reject,
        startTime: performance.now()
      }

      this.taskQueue.push(task)
      this.processNextTask()
    })
  }

  public getStats() {
    return {
      activeWorkers: this.activeWorkers,
      busyWorkers: this.workers.filter(w => w.busy).length,
      queuedTasks: this.taskQueue.length,
      totalProcessed: this.totalProcessed,
      avgProcessingTime: this.totalProcessed > 0 ? this.totalProcessingTime / this.totalProcessed : 0
    }
  }

  public async terminate(): Promise<void> {
    const terminationPromises = this.workers.map(workerInstance => 
      workerInstance.worker.terminate()
    )

    await Promise.all(terminationPromises)
    this.workers = []
    this.activeWorkers = 0
    this.taskQueue = []
  }
}
