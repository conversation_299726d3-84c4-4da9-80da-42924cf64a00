/**
 * Scan Cache Service
 * Manages caching of mod analysis results to improve startup performance
 * Implements intelligent cache invalidation based on file modification times
 */

import type { AnalyzedPackage } from '../../types/analysis';

interface CacheEntry {
  filePath: string;
  fileName: string;
  lastModified: number;
  fileSize: number;
  analysisResult: AnalyzedPackage;
  cacheTime: number;
  version: string;
}

interface CacheMetadata {
  version: string;
  lastCleanup: number;
  totalEntries: number;
  totalSize: number;
}

export class ScanCacheService {
  private static readonly CACHE_VERSION = '1.0.0';
  private static readonly CACHE_KEY = 'simonitor-scan-cache';
  private static readonly METADATA_KEY = 'simonitor-cache-metadata';
  private static readonly MAX_CACHE_AGE = 30 * 24 * 60 * 60 * 1000; // 30 days
  private static readonly MAX_CACHE_SIZE = 100 * 1024 * 1024; // 100MB
  private static readonly CLEANUP_INTERVAL = 7 * 24 * 60 * 60 * 1000; // 7 days

  /**
   * Get cached analysis result for a file
   */
  static async getCachedResult(filePath: string, fileStats: { lastModified: number; size: number }): Promise<AnalyzedPackage | null> {
    try {
      const cache = this.getCache();
      const entry = cache[filePath];

      if (!entry) {
        return null;
      }

      // Check if file has been modified
      if (entry.lastModified !== fileStats.lastModified || entry.fileSize !== fileStats.size) {
        console.log(`🗑️ [ScanCache] File modified, invalidating cache for: ${filePath}`);
        this.removeCacheEntry(filePath);
        return null;
      }

      // Check cache age
      const age = Date.now() - entry.cacheTime;
      if (age > this.MAX_CACHE_AGE) {
        console.log(`🗑️ [ScanCache] Cache expired for: ${filePath}`);
        this.removeCacheEntry(filePath);
        return null;
      }

      // Check version compatibility
      if (entry.version !== this.CACHE_VERSION) {
        console.log(`🗑️ [ScanCache] Version mismatch, invalidating cache for: ${filePath}`);
        this.removeCacheEntry(filePath);
        return null;
      }

      console.log(`✅ [ScanCache] Cache hit for: ${filePath}`);
      return entry.analysisResult;

    } catch (error) {
      console.warn('[ScanCache] Failed to get cached result:', error);
      return null;
    }
  }

  /**
   * Store analysis result in cache
   */
  static async setCachedResult(
    filePath: string, 
    fileName: string,
    fileStats: { lastModified: number; size: number },
    analysisResult: AnalyzedPackage
  ): Promise<void> {
    try {
      const cache = this.getCache();
      
      const entry: CacheEntry = {
        filePath,
        fileName,
        lastModified: fileStats.lastModified,
        fileSize: fileStats.size,
        analysisResult,
        cacheTime: Date.now(),
        version: this.CACHE_VERSION,
      };

      cache[filePath] = entry;
      
      // Update metadata
      this.updateMetadata();
      
      // Check if cleanup is needed
      await this.performMaintenanceIfNeeded();
      
      // Save to localStorage
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
      
      console.log(`💾 [ScanCache] Cached result for: ${filePath}`);

    } catch (error) {
      console.warn('[ScanCache] Failed to cache result:', error);
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): {
    totalEntries: number;
    totalSize: number;
    hitRate: number;
    oldestEntry: number;
    newestEntry: number;
  } {
    try {
      const cache = this.getCache();
      const entries = Object.values(cache);
      
      if (entries.length === 0) {
        return {
          totalEntries: 0,
          totalSize: 0,
          hitRate: 0,
          oldestEntry: 0,
          newestEntry: 0,
        };
      }

      const totalSize = this.calculateCacheSize();
      const cacheTimes = entries.map(e => e.cacheTime);
      
      return {
        totalEntries: entries.length,
        totalSize,
        hitRate: this.getHitRate(),
        oldestEntry: Math.min(...cacheTimes),
        newestEntry: Math.max(...cacheTimes),
      };

    } catch (error) {
      console.warn('[ScanCache] Failed to get cache stats:', error);
      return {
        totalEntries: 0,
        totalSize: 0,
        hitRate: 0,
        oldestEntry: 0,
        newestEntry: 0,
      };
    }
  }

  /**
   * Clear entire cache
   */
  static clearCache(): void {
    try {
      localStorage.removeItem(this.CACHE_KEY);
      localStorage.removeItem(this.METADATA_KEY);
      console.log('🗑️ [ScanCache] Cache cleared');
    } catch (error) {
      console.warn('[ScanCache] Failed to clear cache:', error);
    }
  }

  /**
   * Remove specific cache entry
   */
  static removeCacheEntry(filePath: string): void {
    try {
      const cache = this.getCache();
      delete cache[filePath];
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
      this.updateMetadata();
    } catch (error) {
      console.warn('[ScanCache] Failed to remove cache entry:', error);
    }
  }

  /**
   * Perform cache maintenance (cleanup old entries, size management)
   */
  static async performMaintenance(): Promise<void> {
    try {
      console.log('🧹 [ScanCache] Starting cache maintenance...');
      
      const cache = this.getCache();
      const entries = Object.entries(cache);
      const now = Date.now();
      
      // Remove expired entries
      let removedExpired = 0;
      for (const [filePath, entry] of entries) {
        const age = now - entry.cacheTime;
        if (age > this.MAX_CACHE_AGE) {
          delete cache[filePath];
          removedExpired++;
        }
      }

      // Check cache size and remove oldest entries if needed
      let removedForSize = 0;
      const currentSize = this.calculateCacheSize();
      if (currentSize > this.MAX_CACHE_SIZE) {
        const remainingEntries = Object.entries(cache);
        remainingEntries.sort((a, b) => a[1].cacheTime - b[1].cacheTime);
        
        let sizeToRemove = currentSize - this.MAX_CACHE_SIZE;
        for (const [filePath, entry] of remainingEntries) {
          if (sizeToRemove <= 0) break;
          
          const entrySize = JSON.stringify(entry).length * 2; // Rough size estimate
          delete cache[filePath];
          sizeToRemove -= entrySize;
          removedForSize++;
        }
      }

      // Save cleaned cache
      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
      this.updateMetadata();
      
      console.log(`🧹 [ScanCache] Maintenance complete. Removed ${removedExpired} expired entries, ${removedForSize} for size limit`);

    } catch (error) {
      console.warn('[ScanCache] Cache maintenance failed:', error);
    }
  }

  /**
   * Check if maintenance is needed and perform it
   */
  private static async performMaintenanceIfNeeded(): Promise<void> {
    try {
      const metadata = this.getMetadata();
      const timeSinceLastCleanup = Date.now() - metadata.lastCleanup;
      
      if (timeSinceLastCleanup > this.CLEANUP_INTERVAL) {
        await this.performMaintenance();
      }
    } catch (error) {
      console.warn('[ScanCache] Failed to check maintenance needs:', error);
    }
  }

  /**
   * Get cache from localStorage
   */
  private static getCache(): Record<string, CacheEntry> {
    try {
      const cached = localStorage.getItem(this.CACHE_KEY);
      return cached ? JSON.parse(cached) : {};
    } catch (error) {
      console.warn('[ScanCache] Failed to parse cache, returning empty cache:', error);
      return {};
    }
  }

  /**
   * Get cache metadata
   */
  private static getMetadata(): CacheMetadata {
    try {
      const metadata = localStorage.getItem(this.METADATA_KEY);
      return metadata ? JSON.parse(metadata) : {
        version: this.CACHE_VERSION,
        lastCleanup: Date.now(),
        totalEntries: 0,
        totalSize: 0,
      };
    } catch (error) {
      return {
        version: this.CACHE_VERSION,
        lastCleanup: Date.now(),
        totalEntries: 0,
        totalSize: 0,
      };
    }
  }

  /**
   * Update cache metadata
   */
  private static updateMetadata(): void {
    try {
      const cache = this.getCache();
      const metadata: CacheMetadata = {
        version: this.CACHE_VERSION,
        lastCleanup: this.getMetadata().lastCleanup,
        totalEntries: Object.keys(cache).length,
        totalSize: this.calculateCacheSize(),
      };
      
      localStorage.setItem(this.METADATA_KEY, JSON.stringify(metadata));
    } catch (error) {
      console.warn('[ScanCache] Failed to update metadata:', error);
    }
  }

  /**
   * Calculate total cache size in bytes
   */
  private static calculateCacheSize(): number {
    try {
      const cache = this.getCache();
      const cacheString = JSON.stringify(cache);
      return cacheString.length * 2; // Rough estimate (UTF-16)
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get cache hit rate (placeholder - would need tracking)
   */
  private static getHitRate(): number {
    // This would require tracking hits/misses over time
    // For now, return a placeholder value
    return 0;
  }
}
