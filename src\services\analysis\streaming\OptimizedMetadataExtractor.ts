/**
 * Optimized Metadata Extractor
 * High-performance metadata extraction with caching and selective processing
 */

import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';
import type { CancellationToken } from '../../../types/analysis-results';

export interface OptimizedMetadataOptions {
    enableStringTableExtraction: boolean;
    enableFilenameExtraction: boolean;
    enableTuningExtraction: boolean;
    enableSimDataExtraction: boolean;
    maxResourcesToScan: number;
    enableCaching: boolean;
    prioritizeEssential: boolean;
}

export interface OptimizedMetadataResult {
    metadata: {
        author?: string;
        version?: string;
        description?: string;
        category?: string;
        tags?: string[];
        [key: string]: any;
    };
    extractedItemNames: string[];
    confidence: number;
    hasStringTable: boolean;
    extractionMetrics: {
        stringTableResources: number;
        tuningResources: number;
        simDataResources: number;
        extractionTime: number;
        cacheHitRate: number;
    };
}

/**
 * High-performance metadata extractor
 */
export class OptimizedMetadataExtractor {
    private static metadataCache = new Map<string, any>();
    private static readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutes

    /**
     * Extract metadata with optimizations
     */
    public async extractMetadata(
        buffer: Buffer,
        filePath: string,
        s4tkPackage: Package | null,
        options: Partial<OptimizedMetadataOptions> = {},
        cancellationToken?: CancellationToken
    ): Promise<OptimizedMetadataResult> {
        const startTime = performance.now();
        
        const opts: OptimizedMetadataOptions = {
            enableStringTableExtraction: true,
            enableFilenameExtraction: true,
            enableTuningExtraction: true,
            enableSimDataExtraction: true,
            maxResourcesToScan: 50,
            enableCaching: true,
            prioritizeEssential: true,
            ...options
        };

        const result: OptimizedMetadataResult = {
            metadata: {},
            extractedItemNames: [],
            confidence: 0,
            hasStringTable: false,
            extractionMetrics: {
                stringTableResources: 0,
                tuningResources: 0,
                simDataResources: 0,
                extractionTime: 0,
                cacheHitRate: 0
            }
        };

        try {
            // Check cache first
            if (opts.enableCaching) {
                const cached = this.getCachedMetadata(filePath, buffer);
                if (cached) {
                    result.extractionMetrics.cacheHitRate = 100;
                    return { ...cached, extractionMetrics: result.extractionMetrics };
                }
            }

            // Extract from filename (always fast)
            if (opts.enableFilenameExtraction) {
                const filenameMetadata = this.extractFromFilename(filePath);
                Object.assign(result.metadata, filenameMetadata);
                result.confidence += filenameMetadata.confidence || 0;
            }

            // Extract from package if available
            if (s4tkPackage) {
                await this.extractFromPackage(s4tkPackage, result, opts, cancellationToken);
            }

            // Normalize confidence
            result.confidence = Math.min(100, result.confidence);

            // Cache result
            if (opts.enableCaching) {
                this.cacheMetadata(filePath, buffer, result);
            }

        } catch (error) {
            console.warn('Metadata extraction error:', error);
            result.metadata.extractionError = error instanceof Error ? error.message : String(error);
        } finally {
            result.extractionMetrics.extractionTime = performance.now() - startTime;
        }

        return result;
    }

    /**
     * Extract metadata from filename
     */
    private extractFromFilename(filePath: string): any {
        const fileName = filePath.split(/[/\\]/).pop() || '';
        const metadata: any = { source: 'filename' };
        let confidence = 10;

        // Extract author from common patterns
        const authorPatterns = [
            /\[([^\]]+)\]/,           // [AuthorName]
            /\(([^)]+)\)/,            // (AuthorName)
            /_by_([^_]+)/i,           // _by_AuthorName
            /^([^_\-\s]+)_/           // AuthorName_
        ];

        for (const pattern of authorPatterns) {
            const match = fileName.match(pattern);
            if (match) {
                metadata.author = match[1].trim();
                confidence += 20;
                break;
            }
        }

        // Extract version
        const versionPattern = /v?(\d+\.?\d*\.?\d*)/i;
        const versionMatch = fileName.match(versionPattern);
        if (versionMatch) {
            metadata.version = versionMatch[1];
            confidence += 15;
        }

        // Extract category hints
        const categoryHints = {
            hair: ['hair', 'hairstyle'],
            clothing: ['clothing', 'outfit', 'top', 'bottom', 'dress', 'shoes'],
            makeup: ['makeup', 'lipstick', 'eyeshadow', 'blush'],
            furniture: ['furniture', 'chair', 'table', 'bed', 'sofa'],
            decor: ['decor', 'decoration', 'plant', 'art'],
            gameplay: ['trait', 'career', 'skill', 'mod']
        };

        const lowerFileName = fileName.toLowerCase();
        for (const [category, keywords] of Object.entries(categoryHints)) {
            if (keywords.some(keyword => lowerFileName.includes(keyword))) {
                metadata.categoryHint = category;
                confidence += 10;
                break;
            }
        }

        metadata.confidence = confidence;
        return metadata;
    }

    /**
     * Extract metadata from package
     */
    private async extractFromPackage(
        s4tkPackage: Package,
        result: OptimizedMetadataResult,
        options: OptimizedMetadataOptions,
        cancellationToken?: CancellationToken
    ): Promise<void> {
        const resourceEntries = Array.from(s4tkPackage.entries());
        let processedCount = 0;

        for (const [key, entry] of resourceEntries) {
            if (cancellationToken?.isCancelled) break;
            if (processedCount >= options.maxResourcesToScan) break;

            const resourceType = key.type;

            try {
                // String table resources
                if (options.enableStringTableExtraction && resourceType === BinaryResourceType.StringTable) {
                    await this.extractFromStringTable(entry, result);
                    result.extractionMetrics.stringTableResources++;
                    result.hasStringTable = true;
                }

                // Tuning resources
                else if (options.enableTuningExtraction && resourceType === 0x62E94D38) {
                    await this.extractFromTuning(entry, result);
                    result.extractionMetrics.tuningResources++;
                }

                // SimData resources
                else if (options.enableSimDataExtraction && resourceType === 0x220557DA) {
                    await this.extractFromSimData(entry, result);
                    result.extractionMetrics.simDataResources++;
                }

                processedCount++;

                // Yield control periodically
                if (processedCount % 10 === 0) {
                    await new Promise(resolve => setImmediate(resolve));
                }

            } catch (error) {
                // Continue processing other resources
                console.warn(`Failed to extract from resource ${resourceType.toString(16)}:`, error);
            }
        }
    }

    /**
     * Extract from string table
     */
    private async extractFromStringTable(entry: any, result: OptimizedMetadataResult): Promise<void> {
        try {
            // Quick string table parsing
            const buffer = entry.value;
            if (!buffer || buffer.length === 0) return;

            // Look for common metadata strings
            const text = buffer.toString('utf8');
            
            // Extract author from common patterns in string tables
            const authorMatch = text.match(/(?:author|creator|by)\s*[:\-]?\s*([^\n\r]{1,50})/i);
            if (authorMatch) {
                result.metadata.author = authorMatch[1].trim();
                result.confidence += 30;
            }

            // Extract description
            const descMatch = text.match(/(?:description|desc)\s*[:\-]?\s*([^\n\r]{1,200})/i);
            if (descMatch) {
                result.metadata.description = descMatch[1].trim();
                result.confidence += 20;
            }

            // Extract item names
            const nameMatches = text.match(/[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*/g);
            if (nameMatches) {
                result.extractedItemNames.push(...nameMatches.slice(0, 10)); // Limit to 10
                result.confidence += 10;
            }

        } catch (error) {
            console.warn('String table extraction error:', error);
        }
    }

    /**
     * Extract from tuning
     */
    private async extractFromTuning(entry: any, result: OptimizedMetadataResult): Promise<void> {
        try {
            const buffer = entry.value;
            if (!buffer || buffer.length === 0) return;

            // Quick XML parsing for tuning data
            const xml = buffer.toString('utf8');
            
            // Extract tuning name
            const nameMatch = xml.match(/<I\s+name="([^"]+)"/);
            if (nameMatch) {
                result.extractedItemNames.push(nameMatch[1]);
                result.confidence += 15;
            }

            // Extract category from tuning
            const categoryMatch = xml.match(/<T\s+name="category">([^<]+)</);
            if (categoryMatch) {
                result.metadata.tuningCategory = categoryMatch[1];
                result.confidence += 10;
            }

        } catch (error) {
            console.warn('Tuning extraction error:', error);
        }
    }

    /**
     * Extract from SimData
     */
    private async extractFromSimData(entry: any, result: OptimizedMetadataResult): Promise<void> {
        try {
            const buffer = entry.value;
            if (!buffer || buffer.length === 0) return;

            // Basic SimData parsing for metadata
            // This is a simplified version - full SimData parsing is complex
            
            result.confidence += 5; // Small confidence boost for having SimData

        } catch (error) {
            console.warn('SimData extraction error:', error);
        }
    }

    /**
     * Get cached metadata
     */
    private getCachedMetadata(filePath: string, buffer: Buffer): OptimizedMetadataResult | null {
        const cacheKey = this.generateCacheKey(filePath, buffer);
        const cached = this.metadataCache.get(cacheKey);
        
        if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
            return cached.data;
        }
        
        return null;
    }

    /**
     * Cache metadata
     */
    private cacheMetadata(filePath: string, buffer: Buffer, result: OptimizedMetadataResult): void {
        const cacheKey = this.generateCacheKey(filePath, buffer);
        
        this.metadataCache.set(cacheKey, {
            data: result,
            timestamp: Date.now()
        });

        // Limit cache size
        if (this.metadataCache.size > 1000) {
            const entries = Array.from(this.metadataCache.entries());
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            // Remove oldest 20%
            const toRemove = Math.floor(entries.length * 0.2);
            for (let i = 0; i < toRemove; i++) {
                this.metadataCache.delete(entries[i][0]);
            }
        }
    }

    /**
     * Generate cache key
     */
    private generateCacheKey(filePath: string, buffer: Buffer): string {
        return `${filePath}-${buffer.length}`;
    }

    /**
     * Clear cache
     */
    public static clearCache(): void {
        this.metadataCache.clear();
    }
}
