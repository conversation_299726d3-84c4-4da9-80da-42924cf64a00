/**
 * Vue 3 Composable: Analysis State Management
 * Specialized state management for mod analysis operations and results
 * Follows Vue 3 Composition API best practices with proper reactivity patterns
 */

import { ref, computed, reactive, watch, nextTick } from 'vue';
import type { AnalyzedPackage } from '../../types/analysis';

export interface AnalysisFilters {
  searchTerm: string;
  category: string;
  author: string;
  qualityRange: [number, number];
  hasErrors: boolean | null;
  hasThumbnails: boolean | null;
  fileExtension: string;
  sortBy: 'name' | 'author' | 'quality' | 'size' | 'date';
  sortOrder: 'asc' | 'desc';
}

export interface AnalysisStats {
  totalMods: number;
  totalSize: number;
  averageQuality: number;
  categoryCounts: Record<string, number>;
  authorCounts: Record<string, number>;
  extensionCounts: Record<string, number>;
  errorCount: number;
  thumbnailCount: number;
}

const defaultFilters: AnalysisFilters = {
  searchTerm: '',
  category: '',
  author: '',
  qualityRange: [0, 100],
  hasErrors: null,
  hasThumbnails: null,
  fileExtension: '',
  sortBy: 'name',
  sortOrder: 'asc',
};

export function useAnalysisState() {
  // Reactive state
  const rawResults = ref<AnalyzedPackage[]>([]);
  const filters = reactive<AnalysisFilters>({ ...defaultFilters });
  const selectedMods = ref<Set<string>>(new Set());
  const currentPage = ref(1);
  const itemsPerPage = ref(25);
  const isLoading = ref(false);
  const lastAnalysisTime = ref<Date | null>(null);
  
  // Computed properties
  const filteredResults = computed(() => {
    let results = [...rawResults.value];
    
    // Apply search filter
    if (filters.searchTerm) {
      const searchLower = filters.searchTerm.toLowerCase();
      results = results.filter(mod => 
        mod.modName?.toLowerCase().includes(searchLower) ||
        mod.fileName?.toLowerCase().includes(searchLower) ||
        mod.author?.toLowerCase().includes(searchLower) ||
        mod.description?.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply category filter
    if (filters.category) {
      results = results.filter(mod => mod.category === filters.category);
    }
    
    // Apply author filter
    if (filters.author) {
      results = results.filter(mod => mod.author === filters.author);
    }
    
    // Apply quality range filter
    results = results.filter(mod => {
      const quality = mod.qualityScore || 0;
      return quality >= filters.qualityRange[0] && quality <= filters.qualityRange[1];
    });
    
    // Apply error filter
    if (filters.hasErrors !== null) {
      results = results.filter(mod => {
        const hasErrors = !!(mod.analysisError || mod.processingError);
        return hasErrors === filters.hasErrors;
      });
    }
    
    // Apply thumbnail filter
    if (filters.hasThumbnails !== null) {
      results = results.filter(mod => {
        const hasThumbnails = !!(mod.thumbnails && mod.thumbnails.length > 0);
        return hasThumbnails === filters.hasThumbnails;
      });
    }
    
    // Apply file extension filter
    if (filters.fileExtension) {
      results = results.filter(mod => mod.fileExtension === filters.fileExtension);
    }
    
    // Apply sorting
    results.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filters.sortBy) {
        case 'name':
          aValue = a.modName || a.fileName || '';
          bValue = b.modName || b.fileName || '';
          break;
        case 'author':
          aValue = a.author || '';
          bValue = b.author || '';
          break;
        case 'quality':
          aValue = a.qualityScore || 0;
          bValue = b.qualityScore || 0;
          break;
        case 'size':
          aValue = a.fileSize || 0;
          bValue = b.fileSize || 0;
          break;
        case 'date':
          aValue = a.lastModified || 0;
          bValue = b.lastModified || 0;
          break;
        default:
          aValue = a.modName || a.fileName || '';
          bValue = b.modName || b.fileName || '';
      }
      
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      } else {
        const comparison = aValue - bValue;
        return filters.sortOrder === 'asc' ? comparison : -comparison;
      }
    });
    
    return results;
  });
  
  const paginatedResults = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return filteredResults.value.slice(start, end);
  });
  
  const totalPages = computed(() => {
    return Math.ceil(filteredResults.value.length / itemsPerPage.value);
  });
  
  const analysisStats = computed((): AnalysisStats => {
    const stats: AnalysisStats = {
      totalMods: rawResults.value.length,
      totalSize: 0,
      averageQuality: 0,
      categoryCounts: {},
      authorCounts: {},
      extensionCounts: {},
      errorCount: 0,
      thumbnailCount: 0,
    };
    
    let qualitySum = 0;
    let qualityCount = 0;
    
    rawResults.value.forEach(mod => {
      // Size calculation
      stats.totalSize += mod.fileSize || 0;
      
      // Quality calculation
      if (mod.qualityScore) {
        qualitySum += mod.qualityScore;
        qualityCount++;
      }
      
      // Category counts
      const category = mod.category || 'Unknown';
      stats.categoryCounts[category] = (stats.categoryCounts[category] || 0) + 1;
      
      // Author counts
      const author = mod.author || 'Unknown';
      stats.authorCounts[author] = (stats.authorCounts[author] || 0) + 1;
      
      // Extension counts
      const extension = mod.fileExtension || '.package';
      stats.extensionCounts[extension] = (stats.extensionCounts[extension] || 0) + 1;
      
      // Error count
      if (mod.analysisError || mod.processingError) {
        stats.errorCount++;
      }
      
      // Thumbnail count
      if (mod.thumbnails && mod.thumbnails.length > 0) {
        stats.thumbnailCount++;
      }
    });
    
    stats.averageQuality = qualityCount > 0 ? qualitySum / qualityCount : 0;
    
    return stats;
  });
  
  const hasSelection = computed(() => selectedMods.value.size > 0);
  const selectedCount = computed(() => selectedMods.value.size);
  
  // Methods
  const setResults = (results: AnalyzedPackage[]) => {
    rawResults.value = results;
    lastAnalysisTime.value = new Date();
    currentPage.value = 1; // Reset to first page
    selectedMods.value.clear(); // Clear selection
  };
  
  const addResults = (results: AnalyzedPackage[]) => {
    rawResults.value.push(...results);
    lastAnalysisTime.value = new Date();
  };
  
  const clearResults = () => {
    rawResults.value = [];
    selectedMods.value.clear();
    currentPage.value = 1;
    lastAnalysisTime.value = null;
  };
  
  const resetFilters = () => {
    Object.assign(filters, defaultFilters);
    currentPage.value = 1;
  };
  
  const updateFilter = <K extends keyof AnalysisFilters>(
    key: K, 
    value: AnalysisFilters[K]
  ) => {
    filters[key] = value;
    currentPage.value = 1; // Reset to first page when filtering
  };
  
  const toggleModSelection = (modId: string) => {
    if (selectedMods.value.has(modId)) {
      selectedMods.value.delete(modId);
    } else {
      selectedMods.value.add(modId);
    }
  };
  
  const selectAllMods = () => {
    filteredResults.value.forEach(mod => {
      const modId = mod.filePath || mod.fileName || '';
      if (modId) selectedMods.value.add(modId);
    });
  };
  
  const clearSelection = () => {
    selectedMods.value.clear();
  };
  
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
    }
  };
  
  const nextPage = () => {
    if (currentPage.value < totalPages.value) {
      currentPage.value++;
    }
  };
  
  const previousPage = () => {
    if (currentPage.value > 1) {
      currentPage.value--;
    }
  };
  
  const setItemsPerPage = (count: number) => {
    itemsPerPage.value = count;
    currentPage.value = 1; // Reset to first page
  };
  
  // Watch for filter changes to reset page
  watch(
    () => ({ ...filters }),
    () => {
      currentPage.value = 1;
    },
    { deep: true }
  );
  
  return {
    // State
    rawResults,
    filters,
    selectedMods,
    currentPage,
    itemsPerPage,
    isLoading,
    lastAnalysisTime,
    
    // Computed
    filteredResults,
    paginatedResults,
    totalPages,
    analysisStats,
    hasSelection,
    selectedCount,
    
    // Methods
    setResults,
    addResults,
    clearResults,
    resetFilters,
    updateFilter,
    toggleModSelection,
    selectAllMods,
    clearSelection,
    goToPage,
    nextPage,
    previousPage,
    setItemsPerPage,
  };
}
