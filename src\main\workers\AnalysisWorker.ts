/**
 * Analysis Worker Thread
 * Handles CPU-intensive mod analysis operations in a separate thread
 * to prevent blocking the main process and enable parallel processing
 */

import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { WorkerAnalysisService } from './WorkerAnalysisService';
import type { EnhancedDetailedAnalysisResult, CancellationToken } from '../../types/analysis-results';

// Worker message types
export interface WorkerMessage {
  type: 'analyze' | 'cancel' | 'ping';
  id: string;
  data?: any;
}

export interface WorkerResponse {
  type: 'result' | 'error' | 'progress' | 'pong';
  id: string;
  data?: any;
  error?: string;
}

export interface AnalysisTask {
  id: string;
  buffer: Buffer;
  filePath: string;
  options?: any;
}

// Worker implementation
if (!isMainThread && parentPort) {
  const analysisService = new WorkerAnalysisService();
  const activeTasks = new Map<string, CancellationToken>();

  // Handle messages from main thread
  parentPort.on('message', async (message: WorkerMessage) => {
    try {
      switch (message.type) {
        case 'analyze':
          await handleAnalysisTask(message);
          break;
        case 'cancel':
          handleCancellation(message);
          break;
        case 'ping':
          sendResponse({ type: 'pong', id: message.id });
          break;
        default:
          sendError(message.id, `Unknown message type: ${message.type}`);
      }
    } catch (error) {
      sendError(message.id, error instanceof Error ? error.message : String(error));
    }
  });

  async function handleAnalysisTask(message: WorkerMessage): Promise<void> {
    const task: AnalysisTask = message.data;
    
    // Create cancellation token
    const cancellationToken: CancellationToken = {
      isCancelled: false
    };
    activeTasks.set(task.id, cancellationToken);

    try {
      // Perform analysis
      const result = await analysisService.detailedAnalyzeAsync(
        task.buffer,
        task.filePath,
        cancellationToken
      );

      // Send result if not cancelled
      if (!cancellationToken.isCancelled) {
        sendResponse({
          type: 'result',
          id: task.id,
          data: result
        });
      }
    } catch (error) {
      if (!cancellationToken.isCancelled) {
        sendError(task.id, error instanceof Error ? error.message : String(error));
      }
    } finally {
      activeTasks.delete(task.id);
    }
  }

  function handleCancellation(message: WorkerMessage): void {
    const taskId = message.data?.taskId;
    const cancellationToken = activeTasks.get(taskId);
    
    if (cancellationToken) {
      cancellationToken.isCancelled = true;
      activeTasks.delete(taskId);
    }
  }

  function sendResponse(response: WorkerResponse): void {
    parentPort?.postMessage(response);
  }

  function sendError(id: string, error: string): void {
    sendResponse({
      type: 'error',
      id,
      error
    });
  }

  // Signal worker is ready
  console.log('🔧 [Worker] Worker thread initialized and ready');
  sendResponse({
    type: 'pong',
    id: 'worker-ready'
  });
}

// Export for main thread usage
export class AnalysisWorkerClient {
  private worker: Worker;
  private pendingTasks = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
  }>();

  constructor() {
    this.worker = new Worker(__filename);
    this.setupMessageHandling();
  }

  private setupMessageHandling(): void {
    this.worker.on('message', (response: WorkerResponse) => {
      const pending = this.pendingTasks.get(response.id);
      
      if (pending) {
        if (response.type === 'result') {
          pending.resolve(response.data);
          this.pendingTasks.delete(response.id);
        } else if (response.type === 'error') {
          pending.reject(new Error(response.error || 'Worker error'));
          this.pendingTasks.delete(response.id);
        }
      }
    });

    this.worker.on('error', (error) => {
      console.error('Worker error:', error);
      // Reject all pending tasks
      for (const [id, pending] of this.pendingTasks) {
        pending.reject(error);
      }
      this.pendingTasks.clear();
    });
  }

  async analyze(buffer: Buffer, filePath: string): Promise<EnhancedDetailedAnalysisResult> {
    const id = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      this.pendingTasks.set(id, { resolve, reject });
      
      this.worker.postMessage({
        type: 'analyze',
        id,
        data: { id, buffer, filePath }
      } as WorkerMessage);
    });
  }

  cancel(taskId: string): void {
    this.worker.postMessage({
      type: 'cancel',
      id: `cancel-${Date.now()}`,
      data: { taskId }
    } as WorkerMessage);
  }

  async ping(): Promise<boolean> {
    const id = `ping-${Date.now()}`;
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(id);
        resolve(false);
      }, 5000);

      this.pendingTasks.set(id, {
        resolve: () => {
          clearTimeout(timeout);
          resolve(true);
        },
        reject: () => {
          clearTimeout(timeout);
          resolve(false);
        }
      });

      this.worker.postMessage({
        type: 'ping',
        id
      } as WorkerMessage);
    });
  }

  terminate(): Promise<number> {
    return this.worker.terminate();
  }
}
