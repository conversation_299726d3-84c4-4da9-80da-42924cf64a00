<template>
  <div class="results-filters mb-lg">
    <div class="flex gap-md items-center flex-wrap">
      <div class="form-group" style="margin-bottom: 0; min-width: 200px;">
        <input
          :value="searchQuery"
          @input="handleSearchInput"
          type="text"
          class="form-input"
          placeholder="Search resources..."
        />
      </div>
      
      <div class="form-group" style="margin-bottom: 0;">
        <select 
          :value="selectedTypeFilter" 
          @change="handleTypeFilterChange"
          class="form-input"
        >
          <option value="">All Types</option>
          <option v-for="type in availableTypes" :key="type" :value="type">
            {{ type }}
          </option>
        </select>
      </div>
      
      <div class="form-group" style="margin-bottom: 0;">
        <select 
          :value="selectedFileFilter" 
          @change="handleFileFilterChange"
          class="form-input"
        >
          <option value="">All Files</option>
          <option v-for="file in availableFiles" :key="file" :value="file">
            {{ getFileName(file) }}
          </option>
        </select>
      </div>
      
      <button 
        v-if="hasActiveFilters" 
        class="btn btn-secondary btn-sm"
        @click="handleClearFilters"
      >
        Clear Filters
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

// Props
interface Props {
  searchQuery: string;
  selectedTypeFilter: string;
  selectedFileFilter: string;
  availableTypes: string[];
  availableFiles: string[];
}

const props = defineProps<Props>();

// Events
interface Emits {
  (e: 'update-search', value: string): void;
  (e: 'update-type-filter', value: string): void;
  (e: 'update-file-filter', value: string): void;
  (e: 'clear-filters'): void;
}

const emit = defineEmits<Emits>();

// Computed
const hasActiveFilters = computed(() => 
  props.searchQuery || props.selectedTypeFilter || props.selectedFileFilter
);

// Methods
function handleSearchInput(event: Event) {
  const target = event.target as HTMLInputElement;
  emit('update-search', target.value);
}

function handleTypeFilterChange(event: Event) {
  const target = event.target as HTMLSelectElement;
  emit('update-type-filter', target.value);
}

function handleFileFilterChange(event: Event) {
  const target = event.target as HTMLSelectElement;
  emit('update-file-filter', target.value);
}

function handleClearFilters() {
  emit('clear-filters');
}

function getFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || filePath;
}
</script>

<style scoped>
.results-filters {
  padding: var(--spacing-md);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
}
</style>
</script>
