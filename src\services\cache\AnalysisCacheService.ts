/**
 * Analysis Cache Service
 * Provides intelligent caching for mod analysis results using file metadata
 * and persistent storage to dramatically improve performance for repeated scans
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { app } from 'electron';
import type { EnhancedDetailedAnalysisResult } from '../../types/analysis-results';

export interface CacheEntry {
  filePath: string;
  fileSize: number;
  modifiedTime: number;
  contentHash: string;
  result: EnhancedDetailedAnalysisResult;
  cachedAt: number;
  accessCount: number;
  lastAccessed: number;
}

export interface CacheStats {
  totalEntries: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  totalSize: number;
  oldestEntry: number;
  newestEntry: number;
}

export interface CacheConfig {
  maxEntries: number;
  maxAge: number; // milliseconds
  maxSize: number; // bytes
  enablePersistence: boolean;
  cacheDirectory: string;
  compressionEnabled: boolean;
}

export class AnalysisCacheService {
  private cache = new Map<string, CacheEntry>();
  private config: CacheConfig;
  private stats: CacheStats;
  private cacheFilePath: string;
  private isLoaded = false;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      maxEntries: config.maxEntries || 10000,
      maxAge: config.maxAge || 7 * 24 * 60 * 60 * 1000, // 7 days
      maxSize: config.maxSize || 100 * 1024 * 1024, // 100MB
      enablePersistence: config.enablePersistence ?? true,
      cacheDirectory: config.cacheDirectory || path.join(app.getPath('userData'), 'cache'),
      compressionEnabled: config.compressionEnabled ?? true,
      ...config
    };

    this.stats = {
      totalEntries: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
      totalSize: 0,
      oldestEntry: Date.now(),
      newestEntry: Date.now()
    };

    this.cacheFilePath = path.join(this.config.cacheDirectory, 'analysis-cache.json');
  }

  async initialize(): Promise<void> {
    if (this.isLoaded) return;

    try {
      // Ensure cache directory exists
      await fs.mkdir(this.config.cacheDirectory, { recursive: true });

      // Load existing cache if persistence is enabled
      if (this.config.enablePersistence) {
        await this.loadCache();
      }

      this.isLoaded = true;
      console.log(`✅ [Cache] Initialized with ${this.cache.size} entries`);
    } catch (error) {
      console.error('❌ [Cache] Failed to initialize:', error);
      this.isLoaded = true; // Continue without cache
    }
  }

  async get(filePath: string): Promise<EnhancedDetailedAnalysisResult | null> {
    if (!this.isLoaded) {
      await this.initialize();
    }

    try {
      // Get file metadata
      const fileStats = await fs.stat(filePath);
      const cacheKey = this.generateCacheKey(filePath);
      const entry = this.cache.get(cacheKey);

      if (!entry) {
        this.stats.cacheMisses++;
        this.updateHitRate();
        return null;
      }

      // Check if cache entry is still valid
      if (!this.isEntryValid(entry, fileStats)) {
        this.cache.delete(cacheKey);
        this.stats.totalEntries--;
        this.stats.cacheMisses++;
        this.updateHitRate();
        return null;
      }

      // Update access statistics
      entry.accessCount++;
      entry.lastAccessed = Date.now();
      this.stats.cacheHits++;
      this.updateHitRate();

      console.log(`🎯 [Cache] Hit for ${path.basename(filePath)}`);
      return entry.result;
    } catch (error) {
      console.error('❌ [Cache] Error getting entry:', error);
      this.stats.cacheMisses++;
      this.updateHitRate();
      return null;
    }
  }

  async set(filePath: string, result: EnhancedDetailedAnalysisResult): Promise<void> {
    if (!this.isLoaded) {
      await this.initialize();
    }

    try {
      const fileStats = await fs.stat(filePath);
      const cacheKey = this.generateCacheKey(filePath);
      const contentHash = await this.generateContentHash(filePath);

      const entry: CacheEntry = {
        filePath,
        fileSize: fileStats.size,
        modifiedTime: fileStats.mtimeMs,
        contentHash,
        result,
        cachedAt: Date.now(),
        accessCount: 1,
        lastAccessed: Date.now()
      };

      // Check if we need to evict entries
      await this.evictIfNecessary();

      this.cache.set(cacheKey, entry);
      this.stats.totalEntries++;
      this.updateStats();

      console.log(`💾 [Cache] Stored ${path.basename(filePath)}`);

      // Persist cache if enabled
      if (this.config.enablePersistence) {
        await this.persistCache();
      }
    } catch (error) {
      console.error('❌ [Cache] Error setting entry:', error);
    }
  }

  private generateCacheKey(filePath: string): string {
    return crypto.createHash('sha256').update(filePath).digest('hex');
  }

  private async generateContentHash(filePath: string): Promise<string> {
    try {
      const buffer = await fs.readFile(filePath);
      return crypto.createHash('sha256').update(buffer).digest('hex');
    } catch (error) {
      // Fallback to file path + stats if reading fails
      const stats = await fs.stat(filePath);
      return crypto.createHash('sha256')
        .update(`${filePath}-${stats.size}-${stats.mtimeMs}`)
        .digest('hex');
    }
  }

  private isEntryValid(entry: CacheEntry, fileStats: fs.Stats): boolean {
    // Check file size and modification time
    if (entry.fileSize !== fileStats.size || entry.modifiedTime !== fileStats.mtimeMs) {
      return false;
    }

    // Check age
    const age = Date.now() - entry.cachedAt;
    if (age > this.config.maxAge) {
      return false;
    }

    return true;
  }

  private async evictIfNecessary(): Promise<void> {
    // Check entry count limit
    if (this.cache.size >= this.config.maxEntries) {
      await this.evictLRU(Math.floor(this.config.maxEntries * 0.1)); // Evict 10%
    }

    // Check size limit
    const currentSize = this.calculateCacheSize();
    if (currentSize > this.config.maxSize) {
      await this.evictLRU(Math.floor(this.cache.size * 0.2)); // Evict 20%
    }

    // Check age limit
    await this.evictExpired();
  }

  private async evictLRU(count: number): Promise<void> {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

    for (let i = 0; i < Math.min(count, entries.length); i++) {
      this.cache.delete(entries[i][0]);
      this.stats.totalEntries--;
    }

    console.log(`🧹 [Cache] Evicted ${Math.min(count, entries.length)} LRU entries`);
  }

  private async evictExpired(): Promise<void> {
    const now = Date.now();
    let evicted = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.cachedAt > this.config.maxAge) {
        this.cache.delete(key);
        this.stats.totalEntries--;
        evicted++;
      }
    }

    if (evicted > 0) {
      console.log(`🧹 [Cache] Evicted ${evicted} expired entries`);
    }
  }

  private calculateCacheSize(): number {
    let size = 0;
    for (const entry of this.cache.values()) {
      size += JSON.stringify(entry).length * 2; // Rough estimate (UTF-16)
    }
    return size;
  }

  private updateHitRate(): void {
    const total = this.stats.cacheHits + this.stats.cacheMisses;
    this.stats.hitRate = total > 0 ? (this.stats.cacheHits / total) * 100 : 0;
  }

  private updateStats(): void {
    this.updateHitRate();
    this.stats.totalSize = this.calculateCacheSize();
    
    if (this.cache.size > 0) {
      const entries = Array.from(this.cache.values());
      this.stats.oldestEntry = Math.min(...entries.map(e => e.cachedAt));
      this.stats.newestEntry = Math.max(...entries.map(e => e.cachedAt));
    }
  }

  private async loadCache(): Promise<void> {
    try {
      const data = await fs.readFile(this.cacheFilePath, 'utf-8');
      const cacheData = JSON.parse(data);
      
      for (const [key, entry] of Object.entries(cacheData)) {
        this.cache.set(key, entry as CacheEntry);
      }
      
      this.stats.totalEntries = this.cache.size;
      this.updateStats();
      
      console.log(`📂 [Cache] Loaded ${this.cache.size} entries from disk`);
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        console.error('❌ [Cache] Error loading cache:', error);
      }
    }
  }

  private async persistCache(): Promise<void> {
    try {
      const cacheData = Object.fromEntries(this.cache.entries());
      await fs.writeFile(this.cacheFilePath, JSON.stringify(cacheData, null, 2));
    } catch (error) {
      console.error('❌ [Cache] Error persisting cache:', error);
    }
  }

  getStats(): CacheStats {
    this.updateStats();
    return { ...this.stats };
  }

  async clear(): Promise<void> {
    this.cache.clear();
    this.stats = {
      totalEntries: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
      totalSize: 0,
      oldestEntry: Date.now(),
      newestEntry: Date.now()
    };

    if (this.config.enablePersistence) {
      try {
        await fs.unlink(this.cacheFilePath);
      } catch (error) {
        // Ignore if file doesn't exist
      }
    }

    console.log('🧹 [Cache] Cleared all entries');
  }

  async shutdown(): Promise<void> {
    if (this.config.enablePersistence && this.cache.size > 0) {
      await this.persistCache();
    }
    console.log('✅ [Cache] Shutdown complete');
  }
}
