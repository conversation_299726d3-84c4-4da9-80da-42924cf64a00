/**
 * Simonitor Utility Classes v1.0
 * Comprehensive utility class system following design system patterns
 * Replaces Tailwind @apply usage with consistent design system utilities
 */

/* ===== SPACING UTILITIES ===== */
/* Margin utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-5 { margin: var(--space-5); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }
.m-10 { margin: var(--space-10); }
.m-12 { margin: var(--space-12); }

/* Margin directional */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }
.mt-6 { margin-top: var(--space-6); }
.mt-8 { margin-top: var(--space-8); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }

/* Padding utilities */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-5 { padding: var(--space-5); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Padding directional */
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-3 { padding-top: var(--space-3); }
.pt-4 { padding-top: var(--space-4); }
.pt-6 { padding-top: var(--space-6); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-3 { padding-bottom: var(--space-3); }
.pb-4 { padding-bottom: var(--space-4); }
.pb-6 { padding-bottom: var(--space-6); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--space-1); }
.pl-2 { padding-left: var(--space-2); }
.pl-3 { padding-left: var(--space-3); }
.pl-4 { padding-left: var(--space-4); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--space-1); }
.pr-2 { padding-right: var(--space-2); }
.pr-3 { padding-right: var(--space-3); }
.pr-4 { padding-right: var(--space-4); }

/* ===== SIZING UTILITIES ===== */
/* Width utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }
.w-4 { width: 16px; }
.w-5 { width: 20px; }
.w-6 { width: 24px; }
.w-8 { width: 32px; }
.w-10 { width: 40px; }
.w-12 { width: 48px; }
.w-16 { width: 64px; }

/* Height utilities */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-fit { height: fit-content; }
.h-4 { height: 16px; }
.h-5 { height: 20px; }
.h-6 { height: 24px; }
.h-8 { height: 32px; }
.h-10 { height: 40px; }
.h-12 { height: 48px; }
.h-16 { height: 64px; }

/* Min/Max utilities */
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }
.max-w-none { max-width: none; }
.max-w-full { max-width: 100%; }

/* ===== FLEXBOX UTILITIES ===== */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

/* Flex alignment */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* Flex grow/shrink */
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-initial { flex: 0 1 auto; }
.flex-none { flex: none; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow { flex-grow: 1; }

/* Gap utilities */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-5 { gap: var(--space-5); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* ===== GRID UTILITIES ===== */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(0, 1fr)); }

/* ===== TYPOGRAPHY UTILITIES ===== */
.text-xs { font-size: var(--text-xs); line-height: var(--leading-tight); }
.text-sm { font-size: var(--text-sm); line-height: var(--leading-normal); }
.text-base { font-size: var(--text-base); line-height: var(--leading-normal); }
.text-lg { font-size: var(--text-lg); line-height: var(--leading-relaxed); }
.text-xl { font-size: var(--text-xl); line-height: var(--leading-relaxed); }
.text-2xl { font-size: var(--text-2xl); line-height: var(--leading-tight); }

/* Font weights */
.font-normal { font-weight: var(--font-normal); }
.font-medium { font-weight: var(--font-medium); }
.font-semibold { font-weight: var(--font-semibold); }
.font-bold { font-weight: var(--font-bold); }

/* Text alignment */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

/* Text colors */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-inverse { color: var(--text-inverse); }

/* Semantic text colors */
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-info { color: var(--info); }

/* ===== BACKGROUND UTILITIES ===== */
.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-tertiary { background-color: var(--bg-tertiary); }
.bg-elevated { background-color: var(--bg-elevated); }
.bg-glass { background-color: var(--bg-glass); }
.bg-subtle { background-color: var(--bg-subtle); }

/* Semantic backgrounds */
.bg-success { background-color: var(--success-bg); }
.bg-warning { background-color: var(--warning-bg); }
.bg-error { background-color: var(--error-bg); }
.bg-info { background-color: var(--info-bg); }

/* ===== BORDER UTILITIES ===== */
.border { border: 1px solid var(--border-light); }
.border-0 { border: 0; }
.border-t { border-top: 1px solid var(--border-light); }
.border-b { border-bottom: 1px solid var(--border-light); }
.border-l { border-left: 1px solid var(--border-light); }
.border-r { border-right: 1px solid var(--border-light); }

/* Border colors */
.border-light { border-color: var(--border-light); }
.border-medium { border-color: var(--border-medium); }
.border-strong { border-color: var(--border-strong); }

/* Border radius */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: 9999px; }

/* ===== POSITION UTILITIES ===== */
.static { position: static; }
.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }
.sticky { position: sticky; }

/* Position values */
.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* ===== DISPLAY UTILITIES ===== */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* ===== OVERFLOW UTILITIES ===== */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-visible { overflow: visible; }

/* ===== CURSOR UTILITIES ===== */
.cursor-pointer { cursor: pointer; }
.cursor-default { cursor: default; }
.cursor-not-allowed { cursor: not-allowed; }

/* ===== TRANSITION UTILITIES ===== */
.transition-all { transition: all var(--duration-200) var(--ease-out); }
.transition-colors { transition: color var(--duration-200) var(--ease-out), background-color var(--duration-200) var(--ease-out), border-color var(--duration-200) var(--ease-out); }
.transition-transform { transition: transform var(--duration-200) var(--ease-out); }

/* ===== SHADOW UTILITIES ===== */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

/* ===== INTERACTIVE UTILITIES ===== */
.hover\:bg-elevated:hover { background-color: var(--bg-elevated); }
.hover\:bg-secondary:hover { background-color: var(--bg-secondary); }
.hover\:text-primary:hover { color: var(--text-primary); }
.hover\:border-medium:hover { border-color: var(--border-medium); }
.hover\:shadow-md:hover { box-shadow: var(--shadow-md); }

.focus\:outline-none:focus { outline: none; }
.focus\:ring:focus { box-shadow: 0 0 0 3px var(--focus-ring); }

/* ===== RESPONSIVE UTILITIES ===== */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:hidden { display: none; }
  .sm\:flex { display: flex; }
  .sm\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .sm\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:flex { display: flex; }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
}
