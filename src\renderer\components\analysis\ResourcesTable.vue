<template>
  <div class="resources-table mt-md">
    <div class="table-container">
      <table class="table">
        <thead>
          <tr>
            <th @click="handleSort('key')" class="sortable">
              Resource Key
              <span v-if="sortField === 'key'" class="sort-indicator">
                {{ sortDirection === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="handleSort('type')" class="sortable">
              Type
              <span v-if="sortField === 'type'" class="sort-indicator">
                {{ sortDirection === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="handleSort('group')" class="sortable">
              Group
              <span v-if="sortField === 'group'" class="sort-indicator">
                {{ sortDirection === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th @click="handleSort('instance')" class="sortable">
              Instance
              <span v-if="sortField === 'instance'" class="sort-indicator">
                {{ sortDirection === 'asc' ? '↑' : '↓' }}
              </span>
            </th>
            <th>Content</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="resource in sortedResources" :key="resource.key">
            <td class="table-mono">{{ resource.key }}</td>
            <td>
              <span class="resource-type">{{ resource.type }}</span>
              <span v-if="resource.isOverride" class="badge badge-warning ml-sm">Override</span>
            </td>
            <td class="table-mono">{{ formatNumber(resource.group) }}</td>
            <td class="table-mono">{{ resource.instance }}</td>
            <td class="content-cell">{{ resource.contentSnippet }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import type { ResourceInfo } from '../../../types/analysis';

// Props
interface Props {
  resources: ResourceInfo[];
  sortField: keyof ResourceInfo;
  sortDirection: 'asc' | 'desc';
}

const props = defineProps<Props>();

// Events
interface Emits {
  (e: 'sort-change', field: keyof ResourceInfo, direction: 'asc' | 'desc'): void;
}

const emit = defineEmits<Emits>();

// Computed
const sortedResources = computed(() => {
  return [...props.resources].sort((a, b) => {
    let aVal = a[props.sortField];
    let bVal = b[props.sortField];
    
    // Handle numeric sorting for group
    if (props.sortField === 'group') {
      aVal = Number(aVal);
      bVal = Number(bVal);
    }
    
    // Convert to string for comparison
    const aStr = String(aVal).toLowerCase();
    const bStr = String(bVal).toLowerCase();
    
    const comparison = aStr.localeCompare(bStr, undefined, { numeric: true });
    return props.sortDirection === 'asc' ? comparison : -comparison;
  });
});

// Methods
function handleSort(field: keyof ResourceInfo) {
  let newDirection: 'asc' | 'desc' = 'asc';
  
  if (props.sortField === field) {
    newDirection = props.sortDirection === 'asc' ? 'desc' : 'asc';
  }
  
  emit('sort-change', field, newDirection);
}

function formatNumber(num: number): string {
  return num.toLocaleString();
}
</script>

<style scoped>
.resources-table {
  background-color: var(--bg-primary);
}

.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.sortable:hover {
  background-color: var(--bg-tertiary);
}

.sort-indicator {
  margin-left: var(--spacing-xs);
  font-weight: bold;
}

.resource-type {
  font-weight: 500;
}

.content-cell {
  max-width: 300px;
  word-wrap: break-word;
}

.ml-sm {
  margin-left: var(--spacing-sm);
}
</style>
</script>
