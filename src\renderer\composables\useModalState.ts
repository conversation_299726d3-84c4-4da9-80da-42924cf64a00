/**
 * Vue 3 Composable: Modal State Management
 * Reusable modal state management for consistent modal behavior across components
 * Follows Vue 3 Composition API best practices with proper reactivity patterns
 */

import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue';

export interface ModalOptions {
  closeOnEscape?: boolean;
  closeOnBackdrop?: boolean;
  preventBodyScroll?: boolean;
  focusTrap?: boolean;
}

const defaultOptions: ModalOptions = {
  closeOnEscape: true,
  closeOnBackdrop: true,
  preventBodyScroll: true,
  focusTrap: true,
};

export function useModalState<T = any>(options: ModalOptions = {}) {
  const mergedOptions = { ...defaultOptions, ...options };
  
  // Reactive state
  const isOpen = ref(false);
  const modalData = ref<T | null>(null);
  const isAnimating = ref(false);
  
  // Computed properties
  const hasData = computed(() => modalData.value !== null);
  
  // Modal control methods
  const openModal = async (data?: T) => {
    console.log('🔓 [useModalState] Opening modal with data:', data);
    
    if (data !== undefined) {
      modalData.value = data;
    }
    
    isAnimating.value = true;
    isOpen.value = true;
    
    // Prevent body scroll if enabled
    if (mergedOptions.preventBodyScroll) {
      document.body.style.overflow = 'hidden';
    }
    
    // Wait for next tick to ensure DOM updates
    await nextTick();
    
    // Focus management
    if (mergedOptions.focusTrap) {
      focusFirstElement();
    }
    
    // Animation complete
    setTimeout(() => {
      isAnimating.value = false;
    }, 300); // Match CSS transition duration
  };
  
  const closeModal = async () => {
    console.log('🔒 [useModalState] Closing modal');
    
    isAnimating.value = true;
    
    // Wait for animation to complete
    setTimeout(async () => {
      isOpen.value = false;
      modalData.value = null;
      isAnimating.value = false;
      
      // Restore body scroll
      if (mergedOptions.preventBodyScroll) {
        document.body.style.overflow = '';
      }
      
      await nextTick();
    }, 300); // Match CSS transition duration
  };
  
  const toggleModal = (data?: T) => {
    if (isOpen.value) {
      closeModal();
    } else {
      openModal(data);
    }
  };
  
  const updateModalData = (data: T) => {
    modalData.value = data;
  };
  
  // Focus management for accessibility
  const focusFirstElement = () => {
    const modal = document.querySelector('[role="dialog"]');
    if (modal) {
      const focusableElements = modal.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      const firstElement = focusableElements[0] as HTMLElement;
      if (firstElement) {
        firstElement.focus();
      }
    }
  };
  
  // Keyboard event handlers
  const handleKeydown = (event: KeyboardEvent) => {
    if (!isOpen.value) return;
    
    if (event.key === 'Escape' && mergedOptions.closeOnEscape) {
      event.preventDefault();
      closeModal();
    }
    
    // Focus trap implementation
    if (event.key === 'Tab' && mergedOptions.focusTrap) {
      const modal = document.querySelector('[role="dialog"]');
      if (modal) {
        const focusableElements = modal.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
        
        if (event.shiftKey) {
          // Shift + Tab
          if (document.activeElement === firstElement) {
            event.preventDefault();
            lastElement.focus();
          }
        } else {
          // Tab
          if (document.activeElement === lastElement) {
            event.preventDefault();
            firstElement.focus();
          }
        }
      }
    }
  };
  
  // Backdrop click handler
  const handleBackdropClick = (event: MouseEvent) => {
    if (!mergedOptions.closeOnBackdrop) return;
    
    const target = event.target as HTMLElement;
    const modal = document.querySelector('[role="dialog"]');
    
    // Only close if clicking the backdrop, not the modal content
    if (modal && !modal.contains(target)) {
      closeModal();
    }
  };
  
  // Lifecycle management
  onMounted(() => {
    document.addEventListener('keydown', handleKeydown);
  });
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown);
    
    // Cleanup: restore body scroll if modal was open
    if (isOpen.value && mergedOptions.preventBodyScroll) {
      document.body.style.overflow = '';
    }
  });
  
  return {
    // State
    isOpen,
    modalData,
    isAnimating,
    
    // Computed
    hasData,
    
    // Methods
    openModal,
    closeModal,
    toggleModal,
    updateModalData,
    handleBackdropClick,
    
    // Utilities
    focusFirstElement,
  };
}

// Specialized modal composables for common use cases
export function useModDetailModal() {
  return useModalState<any>({
    closeOnEscape: true,
    closeOnBackdrop: true,
    preventBodyScroll: true,
    focusTrap: true,
  });
}

export function useConfirmationModal() {
  return useModalState<{
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    onConfirm?: () => void;
    onCancel?: () => void;
  }>({
    closeOnEscape: true,
    closeOnBackdrop: false, // Don't close on backdrop for confirmations
    preventBodyScroll: true,
    focusTrap: true,
  });
}

export function useImagePreviewModal() {
  return useModalState<{
    images: string[];
    currentIndex: number;
    title?: string;
  }>({
    closeOnEscape: true,
    closeOnBackdrop: true,
    preventBodyScroll: true,
    focusTrap: false, // Allow arrow key navigation
  });
}
