/**
 * Vue 3 Composable: Memory Optimization
 * Advanced memory management for large mod collections
 * Implements efficient data structures and cleanup strategies
 */

import { ref, shallowRef, shallowReactive, computed, watch, onUnmounted } from 'vue';

export interface MemoryConfig {
  maxCacheSize: number;
  cleanupInterval: number;
  enableWeakRefs: boolean;
  enableObjectPooling: boolean;
  maxPoolSize: number;
}

export interface MemoryMetrics {
  cacheSize: number;
  poolSize: number;
  totalAllocations: number;
  totalDeallocations: number;
  memoryUsage: number;
  lastCleanup: number;
}

const defaultConfig: MemoryConfig = {
  maxCacheSize: 1000,
  cleanupInterval: 30000, // 30 seconds
  enableWeakRefs: true,
  enableObjectPooling: true,
  maxPoolSize: 100,
};

export function useMemoryOptimization<T extends Record<string, any>>(
  config: Partial<MemoryConfig> = {}
) {
  const mergedConfig = { ...defaultConfig, ...config };
  
  // Memory-optimized reactive state
  const cache = shallowReactive(new Map<string, T>());
  const weakCache = ref<WeakMap<object, T>>(new WeakMap());
  const objectPool = shallowRef<T[]>([]);
  const accessTimes = shallowReactive(new Map<string, number>());
  
  // Memory metrics
  const metrics = shallowReactive<MemoryMetrics>({
    cacheSize: 0,
    poolSize: 0,
    totalAllocations: 0,
    totalDeallocations: 0,
    memoryUsage: 0,
    lastCleanup: Date.now(),
  });
  
  // Cleanup timer
  const cleanupTimer = ref<number>();
  
  // LRU Cache implementation
  const lruCache = {
    get(key: string): T | undefined {
      const item = cache.get(key);
      if (item) {
        // Update access time for LRU
        accessTimes.set(key, Date.now());
        return item;
      }
      return undefined;
    },
    
    set(key: string, value: T): void {
      // Check cache size limit
      if (cache.size >= mergedConfig.maxCacheSize && !cache.has(key)) {
        // Remove least recently used item
        const oldestKey = this.getLRUKey();
        if (oldestKey) {
          this.delete(oldestKey);
        }
      }
      
      cache.set(key, value);
      accessTimes.set(key, Date.now());
      metrics.cacheSize = cache.size;
      metrics.totalAllocations++;
    },
    
    delete(key: string): boolean {
      const deleted = cache.delete(key);
      if (deleted) {
        accessTimes.delete(key);
        metrics.cacheSize = cache.size;
        metrics.totalDeallocations++;
      }
      return deleted;
    },
    
    clear(): void {
      cache.clear();
      accessTimes.clear();
      metrics.cacheSize = 0;
      metrics.totalDeallocations += cache.size;
    },
    
    getLRUKey(): string | undefined {
      let oldestKey: string | undefined;
      let oldestTime = Infinity;
      
      for (const [key, time] of accessTimes) {
        if (time < oldestTime) {
          oldestTime = time;
          oldestKey = key;
        }
      }
      
      return oldestKey;
    },
  };
  
  // Object pooling for frequently created/destroyed objects
  const objectPooling = {
    acquire(): T | undefined {
      if (!mergedConfig.enableObjectPooling) return undefined;
      
      const obj = objectPool.value.pop();
      if (obj) {
        metrics.poolSize = objectPool.value.length;
        return obj;
      }
      return undefined;
    },
    
    release(obj: T): void {
      if (!mergedConfig.enableObjectPooling) return;
      
      // Clean object properties
      Object.keys(obj).forEach(key => {
        delete obj[key];
      });
      
      // Add to pool if under limit
      if (objectPool.value.length < mergedConfig.maxPoolSize) {
        objectPool.value.push(obj);
        metrics.poolSize = objectPool.value.length;
      }
    },
    
    clear(): void {
      objectPool.value = [];
      metrics.poolSize = 0;
    },
  };
  
  // Weak reference utilities
  const weakRefs = {
    set(key: object, value: T): void {
      if (mergedConfig.enableWeakRefs) {
        weakCache.value.set(key, value);
      }
    },
    
    get(key: object): T | undefined {
      if (mergedConfig.enableWeakRefs) {
        return weakCache.value.get(key);
      }
      return undefined;
    },
    
    delete(key: object): boolean {
      if (mergedConfig.enableWeakRefs) {
        return weakCache.value.delete(key);
      }
      return false;
    },
  };
  
  // Memory usage calculation
  const calculateMemoryUsage = (): number => {
    let usage = 0;
    
    // Estimate cache memory usage
    for (const [key, value] of cache) {
      usage += key.length * 2; // String characters (UTF-16)
      usage += JSON.stringify(value).length * 2; // Rough object size
    }
    
    // Add pool memory usage
    usage += objectPool.value.length * 1000; // Rough estimate per object
    
    return usage;
  };
  
  // Cleanup strategies
  const cleanup = {
    // Remove expired items based on access time
    removeExpired(maxAge: number = 300000): number { // 5 minutes default
      const now = Date.now();
      let removed = 0;
      
      for (const [key, accessTime] of accessTimes) {
        if (now - accessTime > maxAge) {
          lruCache.delete(key);
          removed++;
        }
      }
      
      return removed;
    },
    
    // Force cleanup to target size
    forceCleanup(targetSize: number): number {
      let removed = 0;
      
      while (cache.size > targetSize) {
        const lruKey = lruCache.getLRUKey();
        if (lruKey) {
          lruCache.delete(lruKey);
          removed++;
        } else {
          break;
        }
      }
      
      return removed;
    },
    
    // Full cleanup
    full(): void {
      lruCache.clear();
      objectPooling.clear();
      weakCache.value = new WeakMap();
      metrics.lastCleanup = Date.now();
    },
  };
  
  // Automatic cleanup
  const startAutoCleanup = () => {
    if (cleanupTimer.value) return;
    
    cleanupTimer.value = window.setInterval(() => {
      const removed = cleanup.removeExpired();
      metrics.lastCleanup = Date.now();
      
      if (removed > 0) {
        console.log(`🧹 [Memory] Auto cleanup removed ${removed} expired items`);
      }
      
      // Update memory usage
      metrics.memoryUsage = calculateMemoryUsage();
      
      // Force cleanup if memory usage is too high
      if (metrics.memoryUsage > 50 * 1024 * 1024) { // 50MB threshold
        const forcedRemoved = cleanup.forceCleanup(Math.floor(mergedConfig.maxCacheSize * 0.7));
        if (forcedRemoved > 0) {
          console.warn(`🧹 [Memory] Force cleanup removed ${forcedRemoved} items due to high memory usage`);
        }
      }
    }, mergedConfig.cleanupInterval);
  };
  
  const stopAutoCleanup = () => {
    if (cleanupTimer.value) {
      clearInterval(cleanupTimer.value);
      cleanupTimer.value = undefined;
    }
  };
  
  // Optimized data structures
  const createOptimizedArray = <U>(initialData: U[] = []): U[] => {
    // Use regular array for small datasets, consider virtual scrolling for large ones
    if (initialData.length > 1000) {
      console.warn('🚨 [Memory] Large array detected. Consider implementing virtual scrolling.');
    }
    return shallowRef(initialData).value;
  };
  
  const createOptimizedObject = <U extends Record<string, any>>(initialData: U = {} as U): U => {
    // Use shallowReactive for better performance with large objects
    return shallowReactive(initialData);
  };
  
  // Memory-efficient computed properties
  const createMemoizedComputed = <U>(
    computeFn: () => U,
    dependencies: any[] = []
  ) => {
    const memoCache = new Map<string, { value: U; deps: any[] }>();
    
    return computed(() => {
      const depsKey = JSON.stringify(dependencies);
      const cached = memoCache.get(depsKey);
      
      // Check if dependencies changed
      if (cached && JSON.stringify(cached.deps) === depsKey) {
        return cached.value;
      }
      
      // Compute new value
      const value = computeFn();
      memoCache.set(depsKey, { value, deps: [...dependencies] });
      
      // Limit cache size
      if (memoCache.size > 100) {
        const firstKey = memoCache.keys().next().value;
        memoCache.delete(firstKey);
      }
      
      return value;
    });
  };
  
  // Performance monitoring
  const getMemoryReport = () => {
    return {
      metrics: { ...metrics, memoryUsage: calculateMemoryUsage() },
      cacheHitRate: metrics.totalAllocations > 0 ? 
        ((metrics.totalAllocations - metrics.totalDeallocations) / metrics.totalAllocations) * 100 : 0,
      poolEfficiency: mergedConfig.maxPoolSize > 0 ? 
        (metrics.poolSize / mergedConfig.maxPoolSize) * 100 : 0,
      recommendations: getOptimizationRecommendations(),
    };
  };
  
  const getOptimizationRecommendations = (): string[] => {
    const recommendations: string[] = [];
    
    if (metrics.cacheSize > mergedConfig.maxCacheSize * 0.9) {
      recommendations.push('Consider increasing cache size or implementing more aggressive cleanup');
    }
    
    if (metrics.memoryUsage > 25 * 1024 * 1024) { // 25MB
      recommendations.push('High memory usage detected. Consider using virtual scrolling or pagination');
    }
    
    if (metrics.poolSize < mergedConfig.maxPoolSize * 0.1) {
      recommendations.push('Object pool underutilized. Consider reducing pool size');
    }
    
    return recommendations;
  };
  
  // Watch for memory pressure
  watch(() => metrics.memoryUsage, (newUsage) => {
    if (newUsage > 100 * 1024 * 1024) { // 100MB critical threshold
      console.error('🚨 [Memory] Critical memory usage detected:', (newUsage / 1024 / 1024).toFixed(2), 'MB');
      cleanup.forceCleanup(Math.floor(mergedConfig.maxCacheSize * 0.5));
    }
  });
  
  // Initialize
  startAutoCleanup();
  
  // Cleanup on unmount
  onUnmounted(() => {
    stopAutoCleanup();
    cleanup.full();
  });
  
  return {
    // Cache operations
    cache: lruCache,
    weakRefs,
    objectPool: objectPooling,
    
    // Memory management
    cleanup,
    metrics,
    
    // Optimized data structures
    createOptimizedArray,
    createOptimizedObject,
    createMemoizedComputed,
    
    // Monitoring
    getMemoryReport,
    calculateMemoryUsage,
    
    // Controls
    startAutoCleanup,
    stopAutoCleanup,
  };
}
