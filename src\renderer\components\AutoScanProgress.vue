<template>
  <div class="auto-scan-progress" v-if="isVisible">
    <!-- Compact Progress Bar (when scanning in background) -->
    <div v-if="isAutoScanning && !showDetailedProgress" class="progress-compact">
      <div class="progress-header">
        <div class="progress-info">
          <div class="progress-icon">
            <div class="loading-spinner"></div>
          </div>
          <div class="progress-text">
            <span class="progress-label">Scanning mods...</span>
            <span class="progress-stats">{{ processedFiles }}/{{ totalFiles }} files</span>
          </div>
        </div>
        <div class="progress-actions">
          <button 
            v-if="canCancel" 
            @click="$emit('cancel')"
            class="btn btn-sm btn-secondary"
            title="Cancel scan"
          >
            Cancel
          </button>
          <button 
            @click="showDetailedProgress = !showDetailedProgress"
            class="btn btn-sm btn-ghost"
            title="Toggle detailed progress"
          >
            {{ showDetailedProgress ? 'Hide' : 'Details' }}
          </button>
        </div>
      </div>
      
      <div class="progress-bar-container">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <span class="progress-percentage">{{ progressPercentage }}%</span>
      </div>
    </div>

    <!-- Detailed Progress View -->
    <div v-if="isAutoScanning && showDetailedProgress" class="progress-detailed">
      <div class="progress-header">
        <h3 class="progress-title">
          <div class="loading-spinner"></div>
          Scanning Mod Collection
        </h3>
        <div class="progress-actions">
          <button 
            v-if="canCancel" 
            @click="$emit('cancel')"
            class="btn btn-sm btn-secondary"
          >
            Cancel Scan
          </button>
          <button 
            @click="showDetailedProgress = false"
            class="btn btn-sm btn-ghost"
          >
            Minimize
          </button>
        </div>
      </div>

      <div class="progress-stats-grid">
        <div class="stat-item">
          <span class="stat-label">Progress</span>
          <span class="stat-value">{{ processedFiles }}/{{ totalFiles }} files</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Time Remaining</span>
          <span class="stat-value">{{ formattedTimeRemaining }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Current File</span>
          <span class="stat-value current-file">{{ currentFile || 'Preparing...' }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Cache Hit Rate</span>
          <span class="stat-value">{{ Math.round(cacheHitRate * 100) }}%</span>
        </div>
      </div>

      <div class="progress-bar-container">
        <div class="progress-bar large">
          <div 
            class="progress-fill" 
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>
        <span class="progress-percentage">{{ progressPercentage }}%</span>
      </div>
    </div>

    <!-- Scan Complete State -->
    <div v-if="scanStatus === 'complete'" class="progress-complete">
      <div class="complete-header">
        <div class="complete-icon">
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="complete-text">
          <span class="complete-title">Scan Complete!</span>
          <span class="complete-stats">{{ scanResults.length }} mods analyzed</span>
        </div>
      </div>
      <button 
        @click="$emit('clear')"
        class="btn btn-sm btn-ghost"
      >
        Dismiss
      </button>
    </div>

    <!-- Scan Error State -->
    <div v-if="scanStatus === 'error'" class="progress-error">
      <div class="error-header">
        <div class="error-icon">
          <ExclamationTriangleIcon class="w-5 h-5" />
        </div>
        <div class="error-text">
          <span class="error-title">Scan Failed</span>
          <span class="error-message">{{ scanError }}</span>
        </div>
      </div>
      <div class="error-actions">
        <button 
          @click="$emit('retry')"
          class="btn btn-sm btn-primary"
        >
          Retry
        </button>
        <button 
          @click="$emit('clear')"
          class="btn btn-sm btn-ghost"
        >
          Dismiss
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';

// Props
interface Props {
  isAutoScanning: boolean;
  scanStatus: 'idle' | 'scanning' | 'complete' | 'error';
  progressPercentage: number;
  processedFiles: number;
  totalFiles: number;
  currentFile: string;
  formattedTimeRemaining: string;
  cacheHitRate: number;
  canCancel: boolean;
  scanResults: any[];
  scanError: string | null;
  showByDefault?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showByDefault: true,
});

// Emits
defineEmits<{
  cancel: [];
  clear: [];
  retry: [];
}>();

// Local state
const showDetailedProgress = ref(false);

// Computed
const isVisible = computed(() => {
  return props.showByDefault && (
    props.isAutoScanning || 
    props.scanStatus === 'complete' || 
    props.scanStatus === 'error'
  );
});
</script>

<style scoped>
.auto-scan-progress {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

.progress-compact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.progress-detailed {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-icon {
  display: flex;
  align-items: center;
}

.progress-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.progress-label {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.progress-stats {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.progress-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.progress-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.progress-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.stat-label {
  font-size: var(--text-xs);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.current-file {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  word-break: break-all;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar.large {
  height: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--primary-orange));
  border-radius: var(--radius-full);
  transition: width 0.3s ease;
}

.progress-percentage {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-muted);
  min-width: 40px;
  text-align: right;
}

.progress-complete,
.progress-error {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.complete-header,
.error-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.complete-icon {
  color: var(--success-color);
}

.error-icon {
  color: var(--error-color);
}

.complete-text,
.error-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.complete-title,
.error-title {
  font-weight: var(--font-medium);
  color: var(--text-primary);
}

.complete-stats {
  font-size: var(--text-sm);
  color: var(--text-muted);
}

.error-message {
  font-size: var(--text-sm);
  color: var(--error-color);
}

.error-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-light);
  border-top: 2px solid var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .progress-stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .progress-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }
  
  .progress-actions {
    justify-content: flex-end;
  }
}
</style>
