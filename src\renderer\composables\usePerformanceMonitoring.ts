/**
 * Vue 3 Composable: Performance Monitoring
 * Real-time performance tracking and optimization for large mod collections
 * Ensures <45ms processing targets are maintained across all operations
 */

import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue';

export interface PerformanceMetrics {
  renderTime: number;
  componentCount: number;
  memoryUsage: number;
  frameRate: number;
  loadTime: number;
  interactionDelay: number;
  lastUpdate: number;
}

export interface PerformanceThresholds {
  maxRenderTime: number;
  maxMemoryUsage: number;
  minFrameRate: number;
  maxInteractionDelay: number;
}

export interface PerformanceAlert {
  type: 'warning' | 'error' | 'critical';
  metric: keyof PerformanceMetrics;
  value: number;
  threshold: number;
  timestamp: number;
  message: string;
}

const defaultThresholds: PerformanceThresholds = {
  maxRenderTime: 45, // 45ms target
  maxMemoryUsage: 100 * 1024 * 1024, // 100MB
  minFrameRate: 30, // 30 FPS minimum
  maxInteractionDelay: 100, // 100ms max interaction delay
};

export function usePerformanceMonitoring(thresholds: Partial<PerformanceThresholds> = {}) {
  const mergedThresholds = { ...defaultThresholds, ...thresholds };
  
  // Reactive state
  const isMonitoring = ref(false);
  const metrics = reactive<PerformanceMetrics>({
    renderTime: 0,
    componentCount: 0,
    memoryUsage: 0,
    frameRate: 0,
    loadTime: 0,
    interactionDelay: 0,
    lastUpdate: Date.now(),
  });
  
  const alerts = ref<PerformanceAlert[]>([]);
  const performanceHistory = ref<PerformanceMetrics[]>([]);
  
  // Performance observers
  const performanceObserver = ref<PerformanceObserver>();
  const frameRateMonitor = ref<number>();
  const memoryMonitor = ref<number>();
  
  // Frame rate tracking
  const frameCount = ref(0);
  const lastFrameTime = ref(performance.now());
  
  // Computed performance indicators
  const isPerformanceOptimal = computed(() => {
    return (
      metrics.renderTime <= mergedThresholds.maxRenderTime &&
      metrics.frameRate >= mergedThresholds.minFrameRate &&
      metrics.interactionDelay <= mergedThresholds.maxInteractionDelay &&
      metrics.memoryUsage <= mergedThresholds.maxMemoryUsage
    );
  });
  
  const performanceScore = computed(() => {
    const renderScore = Math.max(0, 100 - (metrics.renderTime / mergedThresholds.maxRenderTime) * 100);
    const frameScore = Math.min(100, (metrics.frameRate / mergedThresholds.minFrameRate) * 100);
    const interactionScore = Math.max(0, 100 - (metrics.interactionDelay / mergedThresholds.maxInteractionDelay) * 100);
    const memoryScore = Math.max(0, 100 - (metrics.memoryUsage / mergedThresholds.maxMemoryUsage) * 100);
    
    return Math.round((renderScore + frameScore + interactionScore + memoryScore) / 4);
  });
  
  const criticalAlerts = computed(() => {
    return alerts.value.filter(alert => alert.type === 'critical');
  });
  
  // Performance measurement utilities
  const measureRenderTime = async (renderFunction: () => Promise<void> | void) => {
    const startTime = performance.now();
    
    try {
      await renderFunction();
      await nextTick(); // Wait for DOM updates
    } catch (error) {
      console.error('🚨 [Performance] Render function error:', error);
    }
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    updateMetric('renderTime', renderTime);
    
    // Check threshold and create alert if needed
    if (renderTime > mergedThresholds.maxRenderTime) {
      createAlert('warning', 'renderTime', renderTime, mergedThresholds.maxRenderTime,
        `Render time exceeded target: ${renderTime.toFixed(2)}ms > ${mergedThresholds.maxRenderTime}ms`);
    }
    
    return renderTime;
  };
  
  const measureInteractionDelay = (interactionFunction: () => void) => {
    const startTime = performance.now();
    
    try {
      interactionFunction();
    } catch (error) {
      console.error('🚨 [Performance] Interaction function error:', error);
    }
    
    const endTime = performance.now();
    const interactionDelay = endTime - startTime;
    
    updateMetric('interactionDelay', interactionDelay);
    
    if (interactionDelay > mergedThresholds.maxInteractionDelay) {
      createAlert('warning', 'interactionDelay', interactionDelay, mergedThresholds.maxInteractionDelay,
        `Interaction delay exceeded target: ${interactionDelay.toFixed(2)}ms > ${mergedThresholds.maxInteractionDelay}ms`);
    }
    
    return interactionDelay;
  };
  
  // Memory monitoring
  const updateMemoryUsage = () => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsage = memory.usedJSHeapSize;
      
      updateMetric('memoryUsage', memoryUsage);
      
      if (memoryUsage > mergedThresholds.maxMemoryUsage) {
        createAlert('error', 'memoryUsage', memoryUsage, mergedThresholds.maxMemoryUsage,
          `Memory usage exceeded threshold: ${(memoryUsage / 1024 / 1024).toFixed(2)}MB`);
      }
    }
  };
  
  // Frame rate monitoring
  const updateFrameRate = () => {
    frameCount.value++;
    const currentTime = performance.now();
    const deltaTime = currentTime - lastFrameTime.value;
    
    if (deltaTime >= 1000) { // Update every second
      const fps = Math.round((frameCount.value * 1000) / deltaTime);
      updateMetric('frameRate', fps);
      
      if (fps < mergedThresholds.minFrameRate) {
        createAlert('warning', 'frameRate', fps, mergedThresholds.minFrameRate,
          `Frame rate below minimum: ${fps}fps < ${mergedThresholds.minFrameRate}fps`);
      }
      
      frameCount.value = 0;
      lastFrameTime.value = currentTime;
    }
    
    if (isMonitoring.value) {
      frameRateMonitor.value = requestAnimationFrame(updateFrameRate);
    }
  };
  
  // Performance Observer setup
  const setupPerformanceObserver = () => {
    if ('PerformanceObserver' in window) {
      performanceObserver.value = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'measure') {
            updateMetric('loadTime', entry.duration);
          } else if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            const loadTime = navEntry.loadEventEnd - navEntry.navigationStart;
            updateMetric('loadTime', loadTime);
          }
        });
      });
      
      try {
        performanceObserver.value.observe({ entryTypes: ['measure', 'navigation'] });
      } catch (error) {
        console.warn('🚨 [Performance] PerformanceObserver not fully supported:', error);
      }
    }
  };
  
  // Metric update utility
  const updateMetric = (key: keyof PerformanceMetrics, value: number) => {
    metrics[key] = value;
    metrics.lastUpdate = Date.now();
    
    // Add to history (keep last 100 entries)
    const snapshot = { ...metrics };
    performanceHistory.value.push(snapshot);
    if (performanceHistory.value.length > 100) {
      performanceHistory.value.shift();
    }
  };
  
  // Alert creation
  const createAlert = (
    type: PerformanceAlert['type'],
    metric: keyof PerformanceMetrics,
    value: number,
    threshold: number,
    message: string
  ) => {
    const alert: PerformanceAlert = {
      type,
      metric,
      value,
      threshold,
      timestamp: Date.now(),
      message,
    };
    
    alerts.value.unshift(alert);
    
    // Keep only last 50 alerts
    if (alerts.value.length > 50) {
      alerts.value = alerts.value.slice(0, 50);
    }
    
    // Log critical alerts
    if (type === 'critical') {
      console.error('🚨 [Performance] CRITICAL:', message);
    } else if (type === 'error') {
      console.warn('⚠️ [Performance] ERROR:', message);
    } else {
      console.log('📊 [Performance] WARNING:', message);
    }
  };
  
  // Component counting
  const updateComponentCount = (count: number) => {
    updateMetric('componentCount', count);
  };
  
  // Performance optimization suggestions
  const getOptimizationSuggestions = () => {
    const suggestions: string[] = [];
    
    if (metrics.renderTime > mergedThresholds.maxRenderTime) {
      suggestions.push('Consider implementing virtual scrolling for large lists');
      suggestions.push('Use v-memo for expensive computed properties');
      suggestions.push('Implement lazy loading for images and components');
    }
    
    if (metrics.memoryUsage > mergedThresholds.maxMemoryUsage * 0.8) {
      suggestions.push('Consider using shallowRef for large datasets');
      suggestions.push('Implement component recycling for lists');
      suggestions.push('Clear unused reactive references');
    }
    
    if (metrics.frameRate < mergedThresholds.minFrameRate) {
      suggestions.push('Reduce animation complexity');
      suggestions.push('Use CSS transforms instead of layout changes');
      suggestions.push('Debounce scroll and resize handlers');
    }
    
    if (metrics.interactionDelay > mergedThresholds.maxInteractionDelay) {
      suggestions.push('Use requestIdleCallback for non-critical tasks');
      suggestions.push('Implement progressive enhancement');
      suggestions.push('Optimize event handlers');
    }
    
    return suggestions;
  };
  
  // Performance report
  const getPerformanceReport = () => {
    return {
      metrics: { ...metrics },
      score: performanceScore.value,
      isOptimal: isPerformanceOptimal.value,
      alerts: alerts.value.slice(0, 10), // Last 10 alerts
      suggestions: getOptimizationSuggestions(),
      history: performanceHistory.value.slice(-20), // Last 20 measurements
      thresholds: mergedThresholds,
    };
  };
  
  // Start monitoring
  const startMonitoring = () => {
    if (isMonitoring.value) return;
    
    isMonitoring.value = true;
    
    setupPerformanceObserver();
    
    // Start frame rate monitoring
    frameRateMonitor.value = requestAnimationFrame(updateFrameRate);
    
    // Start memory monitoring (every 5 seconds)
    memoryMonitor.value = window.setInterval(updateMemoryUsage, 5000);
    
    console.log('📊 [Performance] Monitoring started');
  };
  
  // Stop monitoring
  const stopMonitoring = () => {
    isMonitoring.value = false;
    
    if (performanceObserver.value) {
      performanceObserver.value.disconnect();
    }
    
    if (frameRateMonitor.value) {
      cancelAnimationFrame(frameRateMonitor.value);
    }
    
    if (memoryMonitor.value) {
      clearInterval(memoryMonitor.value);
    }
    
    console.log('📊 [Performance] Monitoring stopped');
  };
  
  // Clear alerts
  const clearAlerts = () => {
    alerts.value = [];
  };
  
  // Lifecycle management
  onMounted(() => {
    nextTick(() => {
      startMonitoring();
    });
  });
  
  onUnmounted(() => {
    stopMonitoring();
  });
  
  return {
    // State
    isMonitoring,
    metrics,
    alerts,
    performanceHistory,
    
    // Computed
    isPerformanceOptimal,
    performanceScore,
    criticalAlerts,
    
    // Methods
    measureRenderTime,
    measureInteractionDelay,
    updateComponentCount,
    getOptimizationSuggestions,
    getPerformanceReport,
    startMonitoring,
    stopMonitoring,
    clearAlerts,
  };
}
