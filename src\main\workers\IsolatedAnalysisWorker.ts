/**
 * Isolated Analysis Worker
 * A completely isolated worker that only imports the minimal dependencies needed
 * for mod analysis without any Electron APIs or main-process dependencies
 */

import { Worker, isMainThread, parentPort } from 'worker_threads';
import { Package } from '@s4tk/models';
import { FileType, ModCategory } from '../../types/analysis';

// Worker message types
export interface WorkerMessage {
  type: 'analyze' | 'cancel' | 'ping';
  id: string;
  data?: any;
}

export interface WorkerResponse {
  type: 'result' | 'error' | 'progress' | 'pong';
  id: string;
  data?: any;
  error?: string;
}

export interface AnalysisTask {
  id: string;
  buffer: Buffer;
  filePath: string;
}

// Minimal analysis result for worker
interface WorkerAnalysisResult {
  filePath: string;
  fileSize: number;
  fileType: FileType;
  category: ModCategory;
  subcategory: string;
  resourceCount: number;
  isOverride: boolean;
  resources: any[];
  dependencies: any[];
  conflicts: any[];
  metadata: any;
  processingTime: number;
}

// Worker implementation - only runs in worker thread
if (!isMainThread && parentPort) {
  console.log('🔧 [IsolatedWorker] Worker thread starting...');
  
  const activeTasks = new Map<string, boolean>();

  // Handle messages from main thread
  parentPort.on('message', async (message: WorkerMessage) => {
    try {
      switch (message.type) {
        case 'analyze':
          await handleAnalysisTask(message);
          break;
        case 'cancel':
          handleCancellation(message);
          break;
        case 'ping':
          sendResponse({ type: 'pong', id: message.id });
          break;
        default:
          sendError(message.id, `Unknown message type: ${message.type}`);
      }
    } catch (error) {
      sendError(message.id, error instanceof Error ? error.message : String(error));
    }
  });

  async function handleAnalysisTask(message: WorkerMessage): Promise<void> {
    const task: AnalysisTask = message.data;
    activeTasks.set(task.id, false); // false = not cancelled
    
    try {
      const startTime = Date.now();
      
      // Check if cancelled
      if (activeTasks.get(task.id)) {
        return; // Task was cancelled
      }

      // Perform minimal analysis using only S4TK
      const result = await performMinimalAnalysis(task.buffer, task.filePath);
      result.processingTime = Date.now() - startTime;

      // Send result if not cancelled
      if (!activeTasks.get(task.id)) {
        sendResponse({
          type: 'result',
          id: task.id,
          data: result
        });
      }
    } catch (error) {
      if (!activeTasks.get(task.id)) {
        sendError(task.id, error instanceof Error ? error.message : String(error));
      }
    } finally {
      activeTasks.delete(task.id);
    }
  }

  function handleCancellation(message: WorkerMessage): void {
    const taskId = message.data?.taskId;
    if (activeTasks.has(taskId)) {
      activeTasks.set(taskId, true); // Mark as cancelled
    }
  }

  function sendResponse(response: WorkerResponse): void {
    parentPort?.postMessage(response);
  }

  function sendError(id: string, error: string): void {
    sendResponse({
      type: 'error',
      id,
      error
    });
  }

  // Minimal analysis function using only S4TK
  async function performMinimalAnalysis(buffer: Buffer, filePath: string): Promise<WorkerAnalysisResult> {
    try {
      // Basic file type detection
      const fileName = filePath.split(/[/\\]/).pop() || '';
      const ext = fileName.split('.').pop()?.toLowerCase();
      
      if (ext === 'ts4script') {
        return {
          filePath,
          fileSize: buffer.length,
          fileType: FileType.SCRIPT,
          category: ModCategory.SCRIPT,
          subcategory: 'script',
          resourceCount: 1,
          isOverride: false,
          resources: [],
          dependencies: [],
          conflicts: [],
          metadata: { fileName, fileType: 'script' },
          processingTime: 0
        };
      }

      // Parse package using S4TK
      const s4tkPackage = Package.from(buffer, {
        decompressBuffers: false,
        loadRaw: true
      });

      const resourceCount = s4tkPackage.size;
      const resourceTypes = new Set<number>();
      
      // Collect resource types
      for (const [key] of s4tkPackage.entries()) {
        resourceTypes.add(key.type);
      }

      // Basic categorization based on resource types
      const category = categorizeByResourceTypes(Array.from(resourceTypes));
      
      return {
        filePath,
        fileSize: buffer.length,
        fileType: FileType.PACKAGE,
        category,
        subcategory: category.toLowerCase(),
        resourceCount,
        isOverride: hasOverrideResources(Array.from(resourceTypes)),
        resources: [],
        dependencies: [],
        conflicts: [],
        metadata: { 
          fileName, 
          fileType: 'package',
          resourceTypes: Array.from(resourceTypes),
          s4tkVersion: '0.6.14'
        },
        processingTime: 0
      };

    } catch (error) {
      // Return error result
      return {
        filePath,
        fileSize: buffer.length,
        fileType: FileType.UNKNOWN,
        category: ModCategory.UNKNOWN,
        subcategory: 'error',
        resourceCount: 0,
        isOverride: false,
        resources: [],
        dependencies: [],
        conflicts: [],
        metadata: { 
          error: error instanceof Error ? error.message : String(error),
          fileName: filePath.split(/[/\\]/).pop() || ''
        },
        processingTime: 0
      };
    }
  }

  // Basic categorization logic
  function categorizeByResourceTypes(resourceTypes: number[]): ModCategory {
    // CAS-related resource types
    const casTypes = [0x034AEECB, 0x0354796A, 0x00AE6C67, 0x3C1AF1F2];
    if (resourceTypes.some(type => casTypes.includes(type))) {
      return ModCategory.CAS;
    }

    // Object-related resource types  
    const objectTypes = [0x319E4F1D, 0x0C772E27, 0x00B2D882];
    if (resourceTypes.some(type => objectTypes.includes(type))) {
      return ModCategory.OBJECTS;
    }

    // Tuning resource types
    const tuningTypes = [0x62E94D38];
    if (resourceTypes.some(type => tuningTypes.includes(type))) {
      return ModCategory.GAMEPLAY;
    }

    return ModCategory.UNKNOWN;
  }

  // Check for override resources
  function hasOverrideResources(resourceTypes: number[]): boolean {
    // Common override resource types
    const overrideTypes = [0x62E94D38, 0x220557DA, 0x0C772E27];
    return resourceTypes.some(type => overrideTypes.includes(type));
  }

  // Signal worker is ready
  console.log('✅ [IsolatedWorker] Worker thread initialized and ready');
  sendResponse({
    type: 'pong',
    id: 'worker-ready'
  });
}

// Export for main thread usage
export class IsolatedAnalysisWorkerClient {
  private worker: Worker;
  private pendingTasks = new Map<string, {
    resolve: (value: any) => void;
    reject: (error: Error) => void;
  }>();

  constructor() {
    this.worker = new Worker(__filename);
    this.setupMessageHandling();
  }

  private setupMessageHandling(): void {
    this.worker.on('message', (response: WorkerResponse) => {
      const pending = this.pendingTasks.get(response.id);
      
      if (pending) {
        if (response.type === 'result') {
          pending.resolve(response.data);
          this.pendingTasks.delete(response.id);
        } else if (response.type === 'error') {
          pending.reject(new Error(response.error || 'Worker error'));
          this.pendingTasks.delete(response.id);
        }
      }
    });

    this.worker.on('error', (error) => {
      console.error('❌ [IsolatedWorker] Worker error:', error);
      // Reject all pending tasks
      for (const [id, pending] of this.pendingTasks) {
        pending.reject(error);
      }
      this.pendingTasks.clear();
    });
  }

  async analyze(buffer: Buffer, filePath: string): Promise<any> {
    const id = `task-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    return new Promise((resolve, reject) => {
      this.pendingTasks.set(id, { resolve, reject });
      
      this.worker.postMessage({
        type: 'analyze',
        id,
        data: { id, buffer, filePath }
      } as WorkerMessage);
    });
  }

  async ping(): Promise<boolean> {
    const id = `ping-${Date.now()}`;
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        this.pendingTasks.delete(id);
        resolve(false);
      }, 5000);

      this.pendingTasks.set(id, {
        resolve: () => {
          clearTimeout(timeout);
          resolve(true);
        },
        reject: () => {
          clearTimeout(timeout);
          resolve(false);
        }
      });

      this.worker.postMessage({
        type: 'ping',
        id
      } as WorkerMessage);
    });
  }

  terminate(): Promise<number> {
    return this.worker.terminate();
  }
}
