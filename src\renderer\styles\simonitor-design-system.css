/**
 * Simonitor Design System v4.0
 * Strategic Color System with Research-Based Psychology
 * Comprehensive UI transformation with semantic color mapping
 * Apple-inspired minimalism meets Sims 4 gaming aesthetics
 */

/* Import Strategic Color System */
@import './simonitor-strategic-colors.css';

/* Import Utility Classes */
@import './simonitor-utilities.css';

/* ===== STRATEGIC COLOR SYSTEM INTEGRATION ===== */
:root {
  /* Core Brand Palette - OKLCH for Perceptual Uniformity */
  --coral-hue: 15;
  --orange-hue: 25;
  --purple-hue: 280;
  --teal-hue: 175;
  --charcoal-hue: 30;

  /* Primary Coral (#EE6C4D) - Converted to OKLCH */
  --coral-base: oklch(0.65 0.15 var(--coral-hue));
  --coral-50: oklch(0.97 0.02 var(--coral-hue));
  --coral-100: oklch(0.94 0.04 var(--coral-hue));
  --coral-200: oklch(0.88 0.08 var(--coral-hue));
  --coral-300: oklch(0.82 0.12 var(--coral-hue));
  --coral-400: oklch(0.75 0.14 var(--coral-hue));
  --coral-500: oklch(0.65 0.15 var(--coral-hue)); /* Base #EE6C4D */
  --coral-600: oklch(0.58 0.16 var(--coral-hue));
  --coral-700: oklch(0.48 0.14 var(--coral-hue));
  --coral-800: oklch(0.38 0.12 var(--coral-hue));
  --coral-900: oklch(0.28 0.08 var(--coral-hue));
  --coral-950: oklch(0.18 0.04 var(--coral-hue));

  /* Secondary Orange (#F38D68) - Converted to OKLCH */
  --orange-base: oklch(0.72 0.12 var(--orange-hue));
  --orange-50: oklch(0.97 0.02 var(--orange-hue));
  --orange-100: oklch(0.94 0.04 var(--orange-hue));
  --orange-200: oklch(0.88 0.08 var(--orange-hue));
  --orange-300: oklch(0.82 0.10 var(--orange-hue));
  --orange-400: oklch(0.77 0.11 var(--orange-hue));
  --orange-500: oklch(0.72 0.12 var(--orange-hue)); /* Base #F38D68 */
  --orange-600: oklch(0.65 0.13 var(--orange-hue));
  --orange-700: oklch(0.55 0.12 var(--orange-hue));
  --orange-800: oklch(0.45 0.10 var(--orange-hue));
  --orange-900: oklch(0.35 0.08 var(--orange-hue));
  --orange-950: oklch(0.25 0.04 var(--orange-hue));

  /* Premium Purple (#662C91) - Converted to OKLCH */
  --purple-base: oklch(0.42 0.18 var(--purple-hue));
  --purple-50: oklch(0.97 0.02 var(--purple-hue));
  --purple-100: oklch(0.94 0.04 var(--purple-hue));
  --purple-200: oklch(0.88 0.08 var(--purple-hue));
  --purple-300: oklch(0.78 0.12 var(--purple-hue));
  --purple-400: oklch(0.65 0.15 var(--purple-hue));
  --purple-500: oklch(0.42 0.18 var(--purple-hue)); /* Base #662C91 */
  --purple-600: oklch(0.38 0.19 var(--purple-hue));
  --purple-700: oklch(0.32 0.16 var(--purple-hue));
  --purple-800: oklch(0.26 0.12 var(--purple-hue));
  --purple-900: oklch(0.20 0.08 var(--purple-hue));
  --purple-950: oklch(0.14 0.04 var(--purple-hue));

  /* Success Teal (#17A398) - Converted to OKLCH */
  --teal-base: oklch(0.62 0.12 var(--teal-hue));
  --teal-50: oklch(0.97 0.02 var(--teal-hue));
  --teal-100: oklch(0.94 0.04 var(--teal-hue));
  --teal-200: oklch(0.88 0.08 var(--teal-hue));
  --teal-300: oklch(0.82 0.10 var(--teal-hue));
  --teal-400: oklch(0.72 0.11 var(--teal-hue));
  --teal-500: oklch(0.62 0.12 var(--teal-hue)); /* Base #17A398 */
  --teal-600: oklch(0.55 0.13 var(--teal-hue));
  --teal-700: oklch(0.45 0.12 var(--teal-hue));
  --teal-800: oklch(0.35 0.10 var(--teal-hue));
  --teal-900: oklch(0.25 0.08 var(--teal-hue));
  --teal-950: oklch(0.15 0.04 var(--teal-hue));

  /* Neutral Charcoal (#33312E) - Converted to OKLCH */
  --charcoal-base: oklch(0.22 0.02 var(--charcoal-hue));
  --charcoal-50: oklch(0.98 0.005 var(--charcoal-hue));
  --charcoal-100: oklch(0.96 0.01 var(--charcoal-hue));
  --charcoal-200: oklch(0.92 0.015 var(--charcoal-hue));
  --charcoal-300: oklch(0.86 0.02 var(--charcoal-hue));
  --charcoal-400: oklch(0.68 0.02 var(--charcoal-hue));
  --charcoal-500: oklch(0.52 0.02 var(--charcoal-hue));
  --charcoal-600: oklch(0.42 0.02 var(--charcoal-hue));
  --charcoal-700: oklch(0.32 0.02 var(--charcoal-hue));
  --charcoal-800: oklch(0.22 0.02 var(--charcoal-hue)); /* Base #33312E */
  --charcoal-900: oklch(0.15 0.015 var(--charcoal-hue));
  --charcoal-950: oklch(0.08 0.01 var(--charcoal-hue));
  
  /* ===== VIBRANT GRADIENT COLOR MAPPING ===== */
  /* Dynamic gradient-based color assignments for energetic user experience */

  /* Primary Actions & Branding - Vibrant Lime Green for Energy */
  --primary: var(--lime-green-500);
  --primary-hover: var(--lime-green-600);
  --primary-active: var(--lime-green-700);
  --primary-foreground: var(--neutral-50);
  --primary-bg: var(--lime-green-50);
  --primary-border: var(--lime-green-200);

  /* Secondary Actions - Golden Yellow for Warmth */
  --secondary: var(--golden-yellow-500);
  --secondary-hover: var(--golden-yellow-600);
  --secondary-active: var(--golden-yellow-700);
  --secondary-foreground: var(--neutral-900);
  --secondary-bg: var(--golden-yellow-50);
  --secondary-border: var(--golden-yellow-200);

  /* Success States - Lime Green for Growth & Achievement */
  --success: var(--lime-green-500);
  --success-hover: var(--lime-green-600);
  --success-active: var(--lime-green-700);
  --success-foreground: var(--neutral-50);
  --success-bg: var(--lime-green-50);
  --success-border: var(--lime-green-200);

  /* Warning States - Vibrant Orange for Dynamic Attention */
  --warning: var(--vibrant-orange-500);
  --warning-hover: var(--vibrant-orange-600);
  --warning-active: var(--vibrant-orange-700);
  --warning-foreground: var(--neutral-50);
  --warning-bg: var(--vibrant-orange-50);
  --warning-border: var(--vibrant-orange-200);

  /* Error States - Crimson Red for Critical Issues */
  --error: var(--crimson-red-500);
  --error-hover: var(--crimson-red-600);
  --error-active: var(--crimson-red-700);
  --error-foreground: var(--neutral-50);
  --error-bg: var(--crimson-red-50);
  --error-border: var(--crimson-red-200);
  --error-hover-bg: color-mix(in srgb, var(--crimson-red-50) 70%, var(--crimson-red-500) 30%);

  /* Premium/Quality Indicators - Deep Orange for Authority */
  --premium: var(--deep-orange-500);
  --premium-hover: var(--deep-orange-600);
  --premium-active: var(--deep-orange-700);
  --premium-foreground: var(--neutral-50);
  --premium-bg: var(--deep-orange-50);
  --premium-border: var(--deep-orange-200);

  /* Strategic Intelligence Type Colors - Gradient Mapping */
  --intelligence-script: var(--deep-orange-500);
  --intelligence-script-bg: var(--deep-orange-50);
  --intelligence-script-border: var(--deep-orange-200);
  --intelligence-resource: var(--lime-green-500);
  --intelligence-resource-bg: var(--lime-green-50);
  --intelligence-resource-border: var(--lime-green-200);
  --intelligence-quality: var(--golden-yellow-500);
  --intelligence-quality-bg: var(--golden-yellow-50);
  --intelligence-quality-border: var(--golden-yellow-200);

  /* Background Hierarchy - Dark theme */
  --bg-primary: var(--charcoal-900);
  --bg-secondary: var(--charcoal-800);
  --bg-tertiary: var(--charcoal-700);
  --bg-elevated: var(--charcoal-800);
  --bg-overlay: oklch(0.1 0 0 / 0.6);
  --bg-glass: oklch(0.15 0.01 var(--charcoal-hue) / 0.8);
  --bg-subtle: var(--charcoal-850);

  /* Text Hierarchy - Light text for dark background */
  --text-primary: var(--charcoal-50);
  --text-secondary: var(--charcoal-300);
  --text-tertiary: var(--charcoal-400);
  --text-muted: var(--charcoal-500);
  --text-inverse: var(--charcoal-900);
  --text-accent: var(--vibrant-orange-500);
  
  /* Border Hierarchy - Dark theme */
  --border-light: var(--charcoal-700);
  --border-medium: var(--charcoal-600);
  --border-strong: var(--charcoal-500);
  --border-accent: var(--primary-border);
  --border-success: var(--success-border);
  --border-warning: var(--warning-border);
  --border-error: var(--error-border);
  --border-premium: var(--premium-border);

  /* Interactive States with Progressive Intensity - Dark theme */
  --hover-bg: var(--charcoal-800);
  --hover-border: var(--charcoal-600);
  --active-bg: var(--charcoal-700);
  --active-border: var(--charcoal-500);
  --focus-ring: var(--coral-400);
  --focus-ring-offset: var(--charcoal-900);

  /* Component-specific Colors */
  --card-bg: var(--bg-elevated);
  --card-border: var(--border-light);
  --card-shadow: 0 1px 3px 0 oklch(0.1 0 0 / 0.1), 0 1px 2px -1px oklch(0.1 0 0 / 0.1);
  --card-shadow-hover: 0 4px 6px -1px oklch(0.1 0 0 / 0.1), 0 2px 4px -2px oklch(0.1 0 0 / 0.1);
  --card-shadow-elevated: 0 10px 15px -3px oklch(0.1 0 0 / 0.1), 0 4px 6px -4px oklch(0.1 0 0 / 0.1);

  --input-bg: var(--bg-elevated);
  --input-border: var(--border-light);
  --input-border-hover: var(--border-medium);
  --input-border-focus: var(--coral-400);
  --input-placeholder: var(--text-muted);

  --button-shadow: 0 1px 2px 0 oklch(0.1 0 0 / 0.05);
  --button-shadow-hover: 0 4px 6px -1px oklch(0.1 0 0 / 0.1), 0 2px 4px -2px oklch(0.1 0 0 / 0.1);

  /* Strategic Quality Score Colors - Gradient Progression */
  --quality-excellent: var(--lime-green-600);    /* 90-100% - Best quality */
  --quality-good: var(--golden-yellow-600);      /* 75-89% - Good quality */
  --quality-fair: var(--vibrant-orange-600);     /* 60-74% - Fair quality */
  --quality-poor: var(--crimson-red-600);        /* Below 60% - Poor quality */

  /* Strategic File Type Colors - Gradient Mapping */
  --file-package: var(--lime-green-500);         /* .package files - Primary */
  --file-script: var(--deep-orange-500);         /* .ts4script files - Premium */
  --file-resource: var(--vibrant-orange-500);    /* resource files - Warning */
  --file-unknown: var(--crimson-red-500);        /* unknown/problematic - Error */

  /* Status Colors */
  --status-active: var(--teal-500);
  --status-inactive: var(--charcoal-400);
  --status-loading: var(--coral-500);
  --status-error: var(--coral-600);
  --status-warning: var(--orange-500);
  --status-success: var(--teal-500);

  /* Modern OKLCH Shadows */
  --shadow-xs: 0 1px 2px 0 oklch(0.1 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 oklch(0.1 0 0 / 0.1), 0 1px 2px 0 oklch(0.1 0 0 / 0.06);
  --shadow-md: 0 4px 6px -1px oklch(0.1 0 0 / 0.1), 0 2px 4px -1px oklch(0.1 0 0 / 0.06);
  --shadow-lg: 0 10px 15px -3px oklch(0.1 0 0 / 0.1), 0 4px 6px -2px oklch(0.1 0 0 / 0.05);
  --shadow-xl: 0 20px 25px -5px oklch(0.1 0 0 / 0.1), 0 10px 10px -5px oklch(0.1 0 0 / 0.04);
  --shadow-2xl: 0 25px 50px -12px oklch(0.1 0 0 / 0.25);
  --shadow-subtle: 0 1px 2px 0 oklch(0.1 0 0 / 0.03);
  --shadow-hover: 0 4px 8px -2px oklch(0.1 0 0 / 0.1), 0 2px 4px -1px oklch(0.1 0 0 / 0.06);
  --shadow-accent: 0 0 0 1px var(--primary-bg);
  
  /* Spacing Scale (Apple 8pt grid) */
  --space-0: 0;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  
  /* Border Radius (Apple-inspired) */
  --radius-none: 0;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-3xl: 32px;
  --radius-full: 9999px;
  
  /* Typography Scale */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --font-family-display: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  
  /* Apple-inspired typography scale with improved readability */
  --text-xs: 11px;
  --text-sm: 13px;
  --text-base: 16px;
  --text-lg: 17px;
  --text-xl: 19px;
  --text-2xl: 22px;
  --text-3xl: 28px;
  --text-4xl: 34px;
  --text-5xl: 48px;
  
  --font-thin: 100;
  --font-light: 300;
  --font-normal: 400;
  --font-regular: 400; /* Alias for --font-normal for compatibility */
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;
  
  --leading-none: 1;
  --leading-tight: 1.4;
  --leading-snug: 1.45;
  --leading-normal: 1.5;
  --leading-relaxed: 1.6;
  --leading-loose: 1.8;

  /* Letter Spacing - Apple-inspired for better readability */
  --tracking-tighter: -0.04em;
  --tracking-tight: -0.02em;
  --tracking-normal: 0em;
  --tracking-wide: 0.02em;
  --tracking-wider: 0.04em;
  --tracking-widest: 0.08em;
  
  /* Animation & Transitions */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;
  
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-Index Scale */
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Component Specific */
  --card-padding: var(--space-6);
  --card-radius: var(--radius-lg);
  --card-shadow: var(--shadow-md);
  
  --button-height-sm: 32px;
  --button-height-md: 40px;
  --button-height-lg: 48px;
  --button-padding-x: var(--space-4);
  --button-radius: var(--radius-md);
  
  --input-height: 40px;
  --input-padding-x: var(--space-3);
  --input-radius: var(--radius-md);
  
  /* Breakpoints */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* ===== ADVANCED DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
  :root {
    /* Dark Mode Background Hierarchy */
    --bg-primary: var(--charcoal-950);
    --bg-secondary: var(--charcoal-900);
    --bg-tertiary: var(--charcoal-800);
    --bg-elevated: var(--charcoal-900);
    --bg-glass: oklch(0.15 0.01 var(--charcoal-hue) / 0.8);
    --bg-subtle: var(--charcoal-900);

    /* Dark Mode Text Hierarchy */
    --text-primary: var(--charcoal-50);
    --text-secondary: var(--charcoal-300);
    --text-tertiary: var(--charcoal-400);
    --text-muted: var(--charcoal-500);
    --text-inverse: var(--charcoal-900);

    /* Dark Mode Borders */
    --border-light: var(--charcoal-700);
    --border-medium: var(--charcoal-600);
    --border-strong: var(--charcoal-500);

    /* Dark Mode Interactive States */
    --hover-bg: var(--charcoal-800);
    --hover-border: var(--charcoal-600);
    --active-bg: var(--charcoal-700);
    --active-border: var(--charcoal-500);

    /* Dark Mode Component Colors */
    --card-bg: var(--bg-elevated);
    --card-border: var(--border-light);
    --card-shadow: 0 4px 6px -1px oklch(0.05 0 0 / 0.4), 0 2px 4px -1px oklch(0.05 0 0 / 0.3);
    --card-shadow-hover: 0 8px 12px -2px oklch(0.05 0 0 / 0.4), 0 4px 8px -2px oklch(0.05 0 0 / 0.3);
    --card-shadow-elevated: 0 16px 24px -4px oklch(0.05 0 0 / 0.4), 0 8px 16px -4px oklch(0.05 0 0 / 0.3);

    --input-bg: var(--bg-elevated);
    --input-border: var(--border-light);
    --input-border-hover: var(--border-medium);
    --input-border-focus: var(--coral-400);

    /* Enhanced shadows for dark mode */
    --shadow-xs: 0 1px 2px 0 oklch(0.05 0 0 / 0.1);
    --shadow-sm: 0 1px 3px 0 oklch(0.05 0 0 / 0.2), 0 1px 2px 0 oklch(0.05 0 0 / 0.12);
    --shadow-md: 0 4px 6px -1px oklch(0.05 0 0 / 0.2), 0 2px 4px -1px oklch(0.05 0 0 / 0.12);
    --shadow-lg: 0 10px 15px -3px oklch(0.05 0 0 / 0.2), 0 4px 6px -2px oklch(0.05 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px oklch(0.05 0 0 / 0.2), 0 10px 10px -5px oklch(0.05 0 0 / 0.08);
    --shadow-2xl: 0 25px 50px -12px oklch(0.05 0 0 / 0.4);
  }
}

/* ===== BASE STYLES ===== */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  background: linear-gradient(135deg,
    #1a1a1a 0%,
    color-mix(in srgb, #1a1a1a 85%, #BF3100) 30%,
    color-mix(in srgb, #1a1a1a 75%, #D76A03) 60%,
    color-mix(in srgb, #1a1a1a 70%, #EC9F05) 100%);
  min-height: 100vh;
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* ===== UTILITY CLASSES ===== */

/* Layout */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Sizing */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* Spacing */
.p-0 { padding: var(--space-0); }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }
.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }

.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }

.m-0 { margin: var(--space-0); }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-4 { margin: var(--space-4); }
.m-auto { margin: auto; }

.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }
.mb-6 { margin-bottom: var(--space-6); }
.mb-8 { margin-bottom: var(--space-8); }

/* ===== SMOOTH ANIMATIONS & TRANSITIONS ===== */

/* Animation Variables */
:root {
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
  --transition-bounce: 300ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-smooth: 400ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Transitions */
.transition-all {
  transition: all var(--transition-normal);
}

.transition-colors {
  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

.transition-transform {
  transition: transform var(--transition-normal);
}

.transition-opacity {
  transition: opacity var(--transition-normal);
}

.transition-shadow {
  transition: box-shadow var(--transition-normal);
}

/* Hover Effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-glow:hover {
  box-shadow: 0 0 20px oklch(from var(--primary) l c h / 0.3);
}

.hover-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary);
}

/* Loading Animations */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translate3d(0, -30px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% { background-position: -200px 0; }
  100% { background-position: calc(200px + 100%) 0; }
}

/* Animation Classes */
.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-slide-in-up {
  animation: slideInUp var(--transition-smooth) ease-out;
}

.animate-slide-in-down {
  animation: slideInDown var(--transition-smooth) ease-out;
}

.animate-fade-in {
  animation: fadeIn var(--transition-normal) ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale var(--transition-smooth) ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* ===== COMPREHENSIVE COMPONENT STYLES ===== */

/* ===== MODERN BUTTON SYSTEM 2024-2025 ===== */
/* Apple-inspired minimalism with Sims 4 theming and semantic color mapping */

.btn {
  /* Layout & Structure */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);

  /* Modern Sizing - 2024 trends favor slightly larger touch targets */
  padding: var(--space-3) var(--space-5);
  min-height: 44px; /* WCAG 2.1 AA minimum touch target */
  min-width: 44px;

  /* Modern Aesthetics - Rounded corners trend */
  border-radius: var(--radius-lg); /* Increased from md for 2024 trends */
  border: 1px solid transparent;

  /* Typography - Clean, readable */
  font-size: var(--text-sm);
  font-weight: var(--font-semibold); /* Increased for better hierarchy */
  font-family: var(--font-family-sans);
  line-height: 1.2;
  text-decoration: none;
  white-space: nowrap;

  /* Modern Shadows - Subtle depth */
  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.05); /* Subtle inner highlight */

  /* Smooth Interactions - Modern easing */
  transition: all var(--duration-200) cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
  cursor: pointer;

  /* Focus Ring for Accessibility */
  position: relative;
  isolation: isolate;
}

/* Enhanced Focus States - WCAG 2.1 AA Compliant */
.btn:focus-visible {
  outline: none;
  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    0 0 0 3px color-mix(in srgb, var(--primary) 30%, transparent 70%),
    0 0 0 6px color-mix(in srgb, var(--primary) 15%, transparent 85%);
  z-index: 10;
}

/* Disabled State - Modern approach */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  filter: grayscale(0.3);
}

/* Modern Hover States - Subtle lift with enhanced shadows */
.btn:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 4px 12px -2px oklch(0.1 0 0 / 0.15),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

/* Active State - Modern press feedback */
.btn:active:not(:disabled) {
  transform: translateY(-1px) scale(1.01);
  transition-duration: var(--duration-100);
  box-shadow:
    0 2px 6px -1px oklch(0.1 0 0 / 0.1),
    0 1px 4px -1px oklch(0.1 0 0 / 0.06);
}

/* ===== SEMANTIC BUTTON VARIANTS ===== */
/* Using exact color palette with semantic meanings */

/* Primary Button - Lime Green (#BBDB06) - Main actions */
.btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border-color: var(--primary);

  /* Enhanced shadow with brand color */
  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1),
    0 0 0 0 color-mix(in srgb, var(--primary) 20%, transparent 80%);
}

.btn-primary:hover:not(:disabled) {
  background: color-mix(in srgb, var(--primary) 90%, white 10%);
  border-color: color-mix(in srgb, var(--primary) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--primary) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.btn-primary:active:not(:disabled) {
  background: color-mix(in srgb, var(--primary) 85%, black 15%);
  border-color: color-mix(in srgb, var(--primary) 85%, black 15%);
}

/* Secondary Button - Golden Yellow (#F5BB00) - Secondary actions */
.btn-secondary {
  background: var(--secondary);
  color: var(--secondary-foreground);
  border-color: var(--secondary);

  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-secondary:hover:not(:disabled) {
  background: color-mix(in srgb, var(--secondary) 90%, white 10%);
  border-color: color-mix(in srgb, var(--secondary) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--secondary) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.btn-secondary:active:not(:disabled) {
  background: color-mix(in srgb, var(--secondary) 85%, black 15%);
  border-color: color-mix(in srgb, var(--secondary) 85%, black 15%);
}

/* Success Button - Teal (semantic mapping) - Positive actions */
.btn-success {
  background: var(--success);
  color: var(--success-foreground);
  border-color: var(--success);

  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-success:hover:not(:disabled) {
  background: color-mix(in srgb, var(--success) 90%, white 10%);
  border-color: color-mix(in srgb, var(--success) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--success) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.btn-success:active:not(:disabled) {
  background: color-mix(in srgb, var(--success) 85%, black 15%);
  border-color: color-mix(in srgb, var(--success) 85%, black 15%);
}

/* Warning Button - Vibrant Orange (#EC9F05) - Caution actions */
.btn-warning {
  background: var(--warning);
  color: var(--warning-foreground);
  border-color: var(--warning);

  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-warning:hover:not(:disabled) {
  background: color-mix(in srgb, var(--warning) 90%, white 10%);
  border-color: color-mix(in srgb, var(--warning) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--warning) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.btn-warning:active:not(:disabled) {
  background: color-mix(in srgb, var(--warning) 85%, black 15%);
  border-color: color-mix(in srgb, var(--warning) 85%, black 15%);
}

/* Error Button - Crimson Red (#BF3100) - Destructive actions */
.btn-error {
  background: var(--error);
  color: var(--error-foreground);
  border-color: var(--error);

  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-error:hover:not(:disabled) {
  background: color-mix(in srgb, var(--error) 90%, white 10%);
  border-color: color-mix(in srgb, var(--error) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--error) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.btn-error:active:not(:disabled) {
  background: color-mix(in srgb, var(--error) 85%, black 15%);
  border-color: color-mix(in srgb, var(--error) 85%, black 15%);
}

/* Premium Button - Deep Orange (#D76A03) - Premium features */
.btn-premium {
  background: var(--premium);
  color: var(--premium-foreground);
  border-color: var(--premium);

  /* Special premium styling with gradient */
  background: linear-gradient(135deg,
    var(--premium) 0%,
    color-mix(in srgb, var(--premium) 85%, var(--secondary) 15%) 100%);

  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-premium:hover:not(:disabled) {
  background: linear-gradient(135deg,
    color-mix(in srgb, var(--premium) 90%, white 10%) 0%,
    color-mix(in srgb, var(--premium) 80%, var(--secondary) 20%) 100%);
  border-color: color-mix(in srgb, var(--premium) 90%, white 10%);
  box-shadow:
    0 4px 12px -2px color-mix(in srgb, var(--premium) 40%, transparent 60%),
    0 2px 8px -2px oklch(0.1 0 0 / 0.1),
    inset 0 1px 0 0 oklch(1 0 0 / 0.15);
}

.btn-premium:active:not(:disabled) {
  background: linear-gradient(135deg,
    color-mix(in srgb, var(--premium) 85%, black 15%) 0%,
    color-mix(in srgb, var(--premium) 75%, var(--secondary) 25%) 100%);
  border-color: color-mix(in srgb, var(--premium) 85%, black 15%);
}

/* ===== MODERN OUTLINE & GHOST VARIANTS ===== */

/* Outline Button - Modern transparent with border */
.btn-outline {
  background: transparent;
  color: var(--text-primary);
  border-color: var(--border-medium);

  /* Subtle shadow for depth */
  box-shadow:
    0 1px 2px 0 oklch(0.1 0 0 / 0.05),
    inset 0 1px 0 0 oklch(1 0 0 / 0.05);
}

.btn-outline:hover:not(:disabled) {
  background: color-mix(in srgb, var(--bg-elevated) 80%, var(--primary) 20%);
  border-color: color-mix(in srgb, var(--border-medium) 60%, var(--primary) 40%);
  color: color-mix(in srgb, var(--text-primary) 70%, var(--primary) 30%);

  box-shadow:
    0 2px 4px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px 0 oklch(0.1 0 0 / 0.05),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-outline:active:not(:disabled) {
  background: color-mix(in srgb, var(--bg-elevated) 70%, var(--primary) 30%);
  border-color: var(--primary);
  color: var(--primary);
}

/* Outline Primary - Uses brand color */
.btn-outline-primary {
  background: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover:not(:disabled) {
  background: var(--primary-bg);
  border-color: var(--primary);
  color: var(--primary);

  box-shadow:
    0 2px 4px 0 color-mix(in srgb, var(--primary) 20%, transparent 80%),
    0 1px 2px 0 oklch(0.1 0 0 / 0.05),
    inset 0 1px 0 0 oklch(1 0 0 / 0.1);
}

.btn-outline-primary:active:not(:disabled) {
  background: color-mix(in srgb, var(--primary-bg) 70%, var(--primary) 30%);
  border-color: var(--primary);
  color: var(--primary);
}

/* Ghost Button - Minimal, no borders */
.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border-color: transparent;
  box-shadow: none;

  /* Reduced padding for minimal look */
  padding: var(--space-2) var(--space-3);
}

.btn-ghost:hover:not(:disabled) {
  background: color-mix(in srgb, var(--bg-elevated) 60%, var(--primary) 40%);
  color: color-mix(in srgb, var(--text-primary) 60%, var(--primary) 40%);
  border-color: transparent;

  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1);
}

.btn-ghost:active:not(:disabled) {
  background: color-mix(in srgb, var(--bg-elevated) 50%, var(--primary) 50%);
  color: var(--primary);
}

/* Ghost Primary - Uses brand color */
.btn-ghost-primary {
  background: transparent;
  color: var(--primary);
  border-color: transparent;
  box-shadow: none;
  padding: var(--space-2) var(--space-3);
}

.btn-ghost-primary:hover:not(:disabled) {
  background: var(--primary-bg);
  color: var(--primary);
  border-color: transparent;

  box-shadow:
    0 1px 3px 0 color-mix(in srgb, var(--primary) 15%, transparent 85%),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1);
}

.btn-ghost-primary:active:not(:disabled) {
  background: color-mix(in srgb, var(--primary-bg) 70%, var(--primary) 30%);
  color: var(--primary);
}

/* ===== MODERN BUTTON SIZES ===== */
/* 2024-2025 sizing trends with accessibility considerations */

.btn-xs {
  padding: var(--space-1) var(--space-2);
  font-size: var(--text-xs);
  min-height: 32px; /* Smaller but still accessible */
  min-width: 32px;
  border-radius: var(--radius-md);
}

.btn-sm {
  padding: var(--space-2) var(--space-3);
  font-size: var(--text-xs);
  min-height: 36px;
  min-width: 36px;
  border-radius: var(--radius-md);
}

/* Default size is already defined in .btn base class */

.btn-lg {
  padding: var(--space-4) var(--space-6);
  font-size: var(--text-base);
  min-height: 52px;
  min-width: 52px;
  border-radius: var(--radius-xl);
  font-weight: var(--font-bold);
}

.btn-xl {
  padding: var(--space-5) var(--space-8);
  font-size: var(--text-lg);
  min-height: 60px;
  min-width: 60px;
  border-radius: var(--radius-xl);
  font-weight: var(--font-bold);
}

/* ===== BUTTON GROUPS & COMBINATIONS ===== */
/* Modern button group styling */

.btn-group {
  display: inline-flex;
  border-radius: var(--radius-lg);
  box-shadow: 0 1px 3px 0 oklch(0.1 0 0 / 0.1);
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  border-right-width: 0;
  position: relative;
  z-index: 1;
}

.btn-group .btn:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.btn-group .btn:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right-width: 1px;
}

.btn-group .btn:hover,
.btn-group .btn:focus-visible {
  z-index: 2;
  border-right-width: 1px;
}

.btn-group .btn:hover + .btn,
.btn-group .btn:focus-visible + .btn {
  border-left-width: 0;
}

/* Icon buttons - Modern minimal styling */
.btn-icon {
  padding: var(--space-2);
  min-width: 44px;
  min-height: 44px;
  border-radius: var(--radius-lg);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon.btn-sm {
  padding: var(--space-1);
  min-width: 36px;
  min-height: 36px;
  border-radius: var(--radius-md);
}

.btn-icon.btn-lg {
  padding: var(--space-3);
  min-width: 52px;
  min-height: 52px;
  border-radius: var(--radius-xl);
}

/* ===== ADVANCED BUTTON VARIANTS 2024-2025 ===== */

/* Gradient Button - Modern premium styling */
.btn-gradient {
  background: linear-gradient(135deg,
    var(--primary) 0%,
    color-mix(in srgb, var(--primary) 80%, var(--secondary) 20%) 50%,
    var(--secondary) 100%);
  color: var(--primary-foreground);
  border: none;
  position: relative;
  overflow: hidden;
}

.btn-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent 0%,
    oklch(1 0 0 / 0.2) 50%,
    transparent 100%);
  transition: left var(--duration-300) var(--ease-out);
}

.btn-gradient:hover::before {
  left: 100%;
}

.btn-gradient:hover:not(:disabled) {
  background: linear-gradient(135deg,
    color-mix(in srgb, var(--primary) 90%, white 10%) 0%,
    color-mix(in srgb, var(--primary) 75%, var(--secondary) 25%) 50%,
    color-mix(in srgb, var(--secondary) 90%, white 10%) 100%);
}

/* Neon Button - Modern glow effect */
.btn-neon {
  background: transparent;
  color: var(--primary);
  border: 2px solid var(--primary);
  text-shadow: 0 0 8px color-mix(in srgb, var(--primary) 50%, transparent 50%);
  box-shadow:
    0 0 10px color-mix(in srgb, var(--primary) 30%, transparent 70%),
    inset 0 0 10px color-mix(in srgb, var(--primary) 10%, transparent 90%);
}

.btn-neon:hover:not(:disabled) {
  background: color-mix(in srgb, var(--primary) 10%, transparent 90%);
  color: var(--primary);
  text-shadow: 0 0 12px color-mix(in srgb, var(--primary) 70%, transparent 30%);
  box-shadow:
    0 0 20px color-mix(in srgb, var(--primary) 50%, transparent 50%),
    0 0 40px color-mix(in srgb, var(--primary) 30%, transparent 70%),
    inset 0 0 20px color-mix(in srgb, var(--primary) 20%, transparent 80%);
}

/* Glass Button - Modern glassmorphism */
.btn-glass {
  background: color-mix(in srgb, var(--bg-elevated) 60%, transparent 40%);
  color: var(--text-primary);
  border: 1px solid color-mix(in srgb, var(--border-light) 50%, transparent 50%);
  backdrop-filter: blur(12px) saturate(1.2);
  -webkit-backdrop-filter: blur(12px) saturate(1.2);
}

.btn-glass:hover:not(:disabled) {
  background: color-mix(in srgb, var(--bg-elevated) 70%, var(--primary) 30%);
  border-color: color-mix(in srgb, var(--border-light) 30%, var(--primary) 70%);
  color: color-mix(in srgb, var(--text-primary) 70%, var(--primary) 30%);
  backdrop-filter: blur(16px) saturate(1.4);
  -webkit-backdrop-filter: blur(16px) saturate(1.4);
}

/* Floating Action Button - Material Design inspired */
.btn-fab {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  color: var(--primary-foreground);
  border: none;
  box-shadow:
    0 3px 5px -1px oklch(0.1 0 0 / 0.2),
    0 6px 10px 0 oklch(0.1 0 0 / 0.14),
    0 1px 18px 0 oklch(0.1 0 0 / 0.12);
  position: fixed;
  bottom: var(--space-6);
  right: var(--space-6);
  z-index: 1000;
}

.btn-fab:hover:not(:disabled) {
  transform: translateY(-3px) scale(1.1);
  box-shadow:
    0 5px 8px -2px oklch(0.1 0 0 / 0.25),
    0 8px 15px 1px oklch(0.1 0 0 / 0.18),
    0 3px 25px 2px oklch(0.1 0 0 / 0.15);
}

.btn-fab.btn-sm {
  width: 40px;
  height: 40px;
}

.btn-fab.btn-lg {
  width: 72px;
  height: 72px;
}

/* Split Button - Modern dropdown combination */
.btn-split {
  display: inline-flex;
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow:
    0 1px 3px 0 oklch(0.1 0 0 / 0.1),
    0 1px 2px -1px oklch(0.1 0 0 / 0.1);
}

.btn-split .btn {
  border-radius: 0;
  border-right: none;
  position: relative;
  z-index: 1;
}

.btn-split .btn:first-child {
  border-top-left-radius: var(--radius-lg);
  border-bottom-left-radius: var(--radius-lg);
}

.btn-split .btn:last-child {
  border-top-right-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
  border-right: 1px solid;
  border-left: 1px solid color-mix(in srgb, var(--border-light) 50%, transparent 50%);
  padding-left: var(--space-2);
  padding-right: var(--space-2);
  min-width: auto;
}

.btn-split .btn:hover,
.btn-split .btn:focus-visible {
  z-index: 2;
}

/* Loading Button State */
.btn-loading {
  position: relative;
  color: transparent;
  pointer-events: none;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: btn-spin 0.8s linear infinite;
}

@keyframes btn-spin {
  to {
    transform: rotate(360deg);
  }
}

/* ===== MODERN BUTTON STATE UTILITIES ===== */

/* Pulse Animation for Important Actions */
.btn-pulse {
  animation: btn-pulse-animation 2s infinite;
}

@keyframes btn-pulse-animation {
  0% {
    box-shadow: 0 0 0 0 color-mix(in srgb, var(--primary) 40%, transparent 60%);
  }
  70% {
    box-shadow: 0 0 0 10px color-mix(in srgb, var(--primary) 0%, transparent 100%);
  }
  100% {
    box-shadow: 0 0 0 0 color-mix(in srgb, var(--primary) 0%, transparent 100%);
  }
}

/* Shake Animation for Error States */
.btn-shake {
  animation: btn-shake-animation 0.5s ease-in-out;
}

@keyframes btn-shake-animation {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

/* Success Animation */
.btn-success-animation {
  animation: btn-success-flash 0.6s ease-out;
}

@keyframes btn-success-flash {
  0% { background: var(--success); }
  50% {
    background: color-mix(in srgb, var(--success) 70%, white 30%);
    transform: scale(1.05);
  }
  100% {
    background: var(--success);
    transform: scale(1);
  }
}

/* Modern Button Combinations */
.btn-with-badge {
  position: relative;
}

.btn-with-badge::after {
  content: attr(data-badge);
  position: absolute;
  top: -8px;
  right: -8px;
  background: var(--error);
  color: var(--error-foreground);
  font-size: 10px;
  font-weight: var(--font-bold);
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px oklch(0.1 0 0 / 0.3);
}

/* Responsive Button Utilities */
@media (max-width: 768px) {
  .btn-responsive {
    width: 100%;
    justify-content: center;
  }

  .btn-group-responsive {
    flex-direction: column;
  }

  .btn-group-responsive .btn {
    border-radius: var(--radius-lg);
    border-right-width: 1px;
    border-bottom-width: 0;
  }

  .btn-group-responsive .btn:not(:last-child) {
    border-bottom-width: 0;
  }

  .btn-group-responsive .btn:last-child {
    border-bottom-width: 1px;
  }
}

/* Dark Mode Button Enhancements */
@media (prefers-color-scheme: dark) {
  .btn-glass {
    backdrop-filter: blur(12px) saturate(1.2) brightness(1.1);
    -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(1.1);
  }

  .btn-neon {
    text-shadow: 0 0 12px color-mix(in srgb, var(--primary) 60%, transparent 40%);
  }
}

/* High Contrast Mode Button Enhancements */
@media (prefers-contrast: high) {
  .btn {
    border-width: 2px;
    font-weight: var(--font-bold);
  }

  .btn-outline {
    border-width: 3px;
  }

  .btn-ghost {
    border: 2px solid transparent;
  }

  .btn-ghost:hover {
    border-color: currentColor;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .btn,
  .btn-pulse,
  .btn-shake,
  .btn-success-animation,
  .btn-gradient::before {
    animation: none;
    transition: none;
  }

  .btn:hover,
  .btn:active,
  .btn:focus-visible {
    transform: none;
  }
}

/* Modern Card System */
.card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  padding: var(--card-padding);
  transition: all var(--duration-200) var(--ease-out);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
  border-color: var(--border-medium);
}

.card-elevated {
  box-shadow: var(--card-shadow-elevated);
}

.card-header {
  border-bottom: 1px solid var(--border-light);
  padding-bottom: var(--space-4);
  margin-bottom: var(--space-6);
}

.card-footer {
  border-top: 1px solid var(--border-light);
  padding-top: var(--space-4);
  margin-top: var(--space-6);
}

/* Modern Input System */
.input {
  width: 100%;
  height: var(--input-height);
  padding: 0 var(--input-padding-x);
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: var(--input-radius);
  font-size: var(--text-sm);
  font-family: var(--font-family-sans);
  color: var(--text-primary);
  transition: all var(--duration-200) var(--ease-out);
}

.input::placeholder {
  color: var(--input-placeholder);
}

.input:hover {
  border-color: var(--input-border-hover);
}

.input:focus {
  outline: none;
  border-color: var(--input-border-focus);
  box-shadow: 0 0 0 3px oklch(from var(--input-border-focus) l c h / 0.1);
}

.input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--bg-tertiary);
}

/* Search Input */
.search-input {
  padding-left: var(--space-10);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z' /%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: var(--space-3) center;
  background-size: 16px 16px;
}

/* Select Dropdown */
.select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='%236b7280'%3e%3cpath stroke-linecap='round' stroke-linejoin='round' d='m19.5 8.25-7.5 7.5-7.5-7.5' /%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  background-size: 16px 16px;
  padding-right: var(--space-10);
}

/* Badge System */
.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: 1;
}

.badge-primary {
  background: var(--primary-bg);
  color: var(--primary);
  border: 1px solid var(--primary-border);
}

.badge-secondary {
  background: var(--secondary-bg);
  color: var(--secondary);
  border: 1px solid var(--secondary-border);
}

.badge-success {
  background: var(--success-bg);
  color: var(--success);
  border: 1px solid var(--success-border);
}

.badge-warning {
  background: var(--warning-bg);
  color: var(--warning);
  border: 1px solid var(--warning-border);
}

.badge-error {
  background: var(--error-bg);
  color: var(--error);
  border: 1px solid var(--error-border);
}

.badge-premium {
  background: var(--premium-bg);
  color: var(--premium);
  border: 1px solid var(--premium-border);
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
/* WCAG 2.1 AA Compliance for Typography and Spacing */

/* Text Spacing Requirements (WCAG 1.4.12) */
.accessible-text-spacing {
  line-height: 1.5 !important;
  letter-spacing: 0.12em !important;
  word-spacing: 0.16em !important;
  margin-bottom: 2em !important;
}

/* Focus Ring System for Keyboard Navigation */
.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--focus-ring-offset), 0 0 0 6px var(--focus-ring);
}

/* Enhanced Focus Indicators */
button:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

/* Skip Link for Screen Readers */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: var(--font-semibold);
}

.skip-link:focus {
  top: 6px;
}

/* High Contrast Mode Support - Enhanced for WCAG 2.1 AA */
@media (prefers-contrast: high) {
  :root {
    /* Enhanced contrast ratios for high contrast mode - Dark theme */
    --text-primary: var(--charcoal-50);
    --text-secondary: var(--charcoal-200);
    --text-tertiary: var(--charcoal-300);
    --border-light: var(--charcoal-600);
    --border-medium: var(--charcoal-500);
    --border-strong: var(--charcoal-400);
    --card-shadow: 0 2px 8px 0 oklch(0.1 0 0 / 0.4);
    --button-shadow: 0 2px 4px 0 oklch(0.1 0 0 / 0.3);

    /* Enhanced focus indicators for high contrast */
    --focus-ring: var(--charcoal-100);
    --focus-ring-offset: var(--charcoal-900);

    /* Ensure all interactive elements have sufficient contrast for WCAG 2.1 AA */
    --primary: var(--lime-green-400);
    --secondary: var(--golden-yellow-400);
    --success: var(--lime-green-400);
    --warning: var(--vibrant-orange-400);
    --error: var(--crimson-red-400);
    --premium: var(--deep-orange-400);
  }

  .btn {
    border-width: 2px;
    font-weight: var(--font-semibold);
  }

  .card {
    border-width: 2px;
  }

  .input {
    border-width: 2px;
  }

  /* Enhanced text contrast in high contrast mode - Dark theme */
  .thumbnail-title {
    font-weight: var(--font-bold);
    color: var(--charcoal-50);
  }

  .thumbnail-author {
    font-weight: var(--font-semibold);
    color: var(--charcoal-200);
  }

  /* Enhanced badge contrast */
  .quality-badge,
  .file-type-badge {
    border: 2px solid var(--charcoal-100);
    font-weight: var(--font-bold);
  }

  /* Enhanced focus indicators for high contrast */
  button:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible,
  [tabindex]:focus-visible {
    outline: 3px solid var(--focus-ring);
    outline-offset: 3px;
  }
}

/* ===== ADDITIONAL WCAG 2.1 AA COMPLIANCE FEATURES ===== */

/* Ensure minimum touch target size (44x44px) for mobile accessibility */
@media (pointer: coarse) {
  button,
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  .btn,
  .interactive-primary,
  .interactive-secondary,
  .card-interactive,
  .badge-interactive,
  .icon-btn-interactive {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Enhanced focus indicators for all interactive elements */
.focus-visible-enhanced:focus-visible {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 6px color-mix(in srgb, var(--primary) 20%, transparent 80%);
}

/* Screen reader improvements */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .btn:hover {
    transform: none;
  }

  .card:hover {
    transform: none;
  }
}

/* Screen Reader Only Content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg-elevated);
  color: var(--text-primary);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-medium);
  z-index: var(--z-tooltip);
  transition: top var(--duration-200) var(--ease-out);
}

.skip-link:focus {
  top: 6px;
}

/* ===== ZOOM ACCESSIBILITY SUPPORT ===== */
/* WCAG 2.1 AA: Support for 200% zoom without horizontal scrolling */
@media (min-resolution: 2dppx) and (max-width: 1280px) {
  :root {
    /* Adjust font sizes for better readability at high zoom */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 17px; /* Increased for better zoom readability */
    --text-lg: 19px;
    --text-xl: 21px;
    --text-2xl: 24px;
    --text-3xl: 30px;
  }

  /* Ensure content doesn't break at 200% zoom */
  .container {
    max-width: 100%;
    padding: var(--space-4);
  }

  .thumbnail-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-4);
  }

  /* Adjust modal for zoom accessibility */
  .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
  }
}

/* ===== RESPONSIVE DESIGN ===== */
/* Mobile First Approach */
@media (max-width: 640px) {
  .dashboard-controls {
    padding: var(--space-4);
  }

  .mods-grid {
    grid-template-columns: 1fr;
    padding: 0 var(--space-4) var(--space-4) var(--space-4);
    gap: var(--space-4);
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    height: 48px;
    font-size: var(--text-base);
  }

  .btn {
    min-height: 44px;
    padding: var(--space-3) var(--space-4);
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .mods-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .mods-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (min-width: 1025px) {
  .mods-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .btn,
  .search-input,
  .filter-select {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }

  .card {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  .mods-grid {
    display: block;
  }

  .mod-card {
    margin-bottom: var(--space-4);
  }
}

/* ===== UTILITY CLASSES ===== */
/* Color Utilities */
.text-coral { color: var(--coral-500); }
.text-orange { color: var(--orange-500); }
.text-purple { color: var(--purple-500); }
.text-teal { color: var(--teal-500); }
.text-charcoal { color: var(--charcoal-500); }

.bg-coral { background-color: var(--coral-500); }
.bg-orange { background-color: var(--orange-500); }
.bg-purple { background-color: var(--purple-500); }
.bg-teal { background-color: var(--teal-500); }
.bg-charcoal { background-color: var(--charcoal-500); }

.bg-coral-light { background-color: var(--coral-50); }
.bg-orange-light { background-color: var(--orange-50); }
.bg-purple-light { background-color: var(--purple-50); }
.bg-teal-light { background-color: var(--teal-50); }

.border-coral { border-color: var(--coral-500); }
.border-orange { border-color: var(--orange-500); }
.border-purple { border-color: var(--purple-500); }
.border-teal { border-color: var(--teal-500); }

/* Semantic Color Utilities */
.text-primary-color { color: var(--primary); }
.text-secondary-color { color: var(--secondary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }
.text-premium { color: var(--premium); }

.bg-primary-color { background-color: var(--primary); }
.bg-secondary-color { background-color: var(--secondary); }
.bg-success { background-color: var(--success); }
.bg-warning { background-color: var(--warning); }
.bg-error { background-color: var(--error); }
.bg-premium { background-color: var(--premium); }

/* Quality Score Utilities */
.quality-excellent { color: var(--quality-excellent); }
.quality-good { color: var(--quality-good); }
.quality-fair { color: var(--quality-fair); }
.quality-poor { color: var(--quality-poor); }

.bg-quality-excellent { background-color: var(--lime-green-50); color: var(--quality-excellent); }
.bg-quality-good { background-color: var(--golden-yellow-50); color: var(--quality-good); }
.bg-quality-fair { background-color: var(--vibrant-orange-50); color: var(--quality-fair); }
.bg-quality-poor { background-color: var(--crimson-red-50); color: var(--quality-poor); }

/* File Type Utilities */
.file-package { color: var(--file-package); }
.file-script { color: var(--file-script); }
.file-resource { color: var(--file-resource); }
.file-unknown { color: var(--file-unknown); }

/* Status Utilities */
.status-active { color: var(--status-active); }
.status-inactive { color: var(--status-inactive); }
.status-loading { color: var(--status-loading); }
.status-error { color: var(--status-error); }
.status-warning { color: var(--status-warning); }
.status-success { color: var(--status-success); }

/* ===== PROGRESSIVE INTERACTION STATES ===== */
/* Enhanced interaction states using color-mix for smooth transitions */

/* Base Interactive State Utilities */
.hover-lift {
  transition: transform var(--duration-200) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

.hover-glow {
  transition: box-shadow var(--duration-200) var(--ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px oklch(from var(--coral-500) l c h / 0.3);
}

/* Progressive Color-Mix Interaction States */
.interactive-primary {
  background: var(--primary-bg);
  color: var(--primary);
  border: 1px solid var(--primary-border);
  transition: all var(--duration-200) var(--ease-out);
}

.interactive-primary:hover {
  background: color-mix(in srgb, var(--primary-bg) 70%, var(--primary) 30%);
  border-color: color-mix(in srgb, var(--primary-border) 50%, var(--primary) 50%);
  transform: translateY(-1px);
}

.interactive-primary:active {
  background: color-mix(in srgb, var(--primary-bg) 50%, var(--primary) 50%);
  border-color: var(--primary);
  transform: translateY(0);
}

.interactive-secondary {
  background: var(--secondary-bg);
  color: var(--secondary);
  border: 1px solid var(--secondary-border);
  transition: all var(--duration-200) var(--ease-out);
}

.interactive-secondary:hover {
  background: color-mix(in srgb, var(--secondary-bg) 70%, var(--secondary) 30%);
  border-color: color-mix(in srgb, var(--secondary-border) 50%, var(--secondary) 50%);
  transform: translateY(-1px);
}

.interactive-secondary:active {
  background: color-mix(in srgb, var(--secondary-bg) 50%, var(--secondary) 50%);
  border-color: var(--secondary);
  transform: translateY(0);
}

/* Progressive Background States */
.bg-interactive {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  transition: all var(--duration-300) var(--ease-out);
}

.bg-interactive:hover {
  background: color-mix(in srgb, var(--bg-elevated) 85%, var(--primary) 15%);
  border-color: color-mix(in srgb, var(--border-light) 70%, var(--primary) 30%);
  box-shadow: var(--shadow-md);
}

.bg-interactive:active {
  background: color-mix(in srgb, var(--bg-elevated) 80%, var(--primary) 20%);
  border-color: color-mix(in srgb, var(--border-light) 50%, var(--primary) 50%);
  transform: scale(0.98);
}

/* Subtle Interactive States */
.subtle-interactive {
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid transparent;
  transition: all var(--duration-200) var(--ease-out);
}

.subtle-interactive:hover {
  background: color-mix(in srgb, transparent 70%, var(--primary-bg) 30%);
  color: color-mix(in srgb, var(--text-secondary) 70%, var(--primary) 30%);
  border-color: color-mix(in srgb, transparent 80%, var(--primary-border) 20%);
}

.subtle-interactive:active {
  background: color-mix(in srgb, transparent 50%, var(--primary-bg) 50%);
  color: var(--primary);
  border-color: var(--primary-border);
}

/* Enhanced Card Interactive States */
.card-interactive {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: var(--radius-lg);
  transition: all var(--duration-300) var(--ease-out);
  cursor: pointer;
}

.card-interactive:hover {
  background: color-mix(in srgb, var(--card-bg) 90%, var(--primary) 10%);
  border-color: color-mix(in srgb, var(--card-border) 60%, var(--primary) 40%);
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px) scale(1.01);
}

.card-interactive:active {
  background: color-mix(in srgb, var(--card-bg) 85%, var(--primary) 15%);
  border-color: color-mix(in srgb, var(--card-border) 40%, var(--primary) 60%);
  transform: translateY(-1px) scale(1.005);
  transition-duration: var(--duration-150);
}

/* Progressive Badge States */
.badge-interactive {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-full);
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
}

.badge-interactive:hover {
  background: color-mix(in srgb, var(--bg-secondary) 70%, var(--primary) 30%);
  color: color-mix(in srgb, var(--text-secondary) 50%, var(--primary) 50%);
  border-color: color-mix(in srgb, var(--border-light) 50%, var(--primary) 50%);
  transform: scale(1.05);
}

.badge-interactive:active {
  background: color-mix(in srgb, var(--bg-secondary) 50%, var(--primary) 50%);
  color: var(--primary-foreground);
  border-color: var(--primary);
  transform: scale(1.02);
}

/* Progressive Icon Button States */
.icon-btn-interactive {
  background: var(--bg-elevated);
  color: var(--text-muted);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-2);
  transition: all var(--duration-200) var(--ease-out);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn-interactive:hover {
  background: color-mix(in srgb, var(--bg-elevated) 80%, var(--primary) 20%);
  color: color-mix(in srgb, var(--text-muted) 40%, var(--primary) 60%);
  border-color: color-mix(in srgb, var(--border-light) 60%, var(--primary) 40%);
  transform: translateY(-1px) scale(1.05);
  box-shadow: var(--shadow-sm);
}

.icon-btn-interactive:active {
  background: color-mix(in srgb, var(--bg-elevated) 70%, var(--primary) 30%);
  color: var(--primary);
  border-color: var(--primary);
  transform: translateY(0) scale(1.02);
}

/* Progressive Input States */
.input-interactive {
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: var(--radius-md);
  padding: var(--space-3);
  color: var(--text-primary);
  transition: all var(--duration-200) var(--ease-out);
}

.input-interactive:hover {
  border-color: color-mix(in srgb, var(--input-border) 70%, var(--primary) 30%);
  background: color-mix(in srgb, var(--input-bg) 95%, var(--primary) 5%);
}

.input-interactive:focus {
  outline: none;
  border-color: var(--primary);
  background: color-mix(in srgb, var(--input-bg) 90%, var(--primary) 10%);
  box-shadow: 0 0 0 3px color-mix(in srgb, transparent 70%, var(--primary) 30%);
}

/* Animation Utilities */
.animate-fade-in {
  animation: fadeIn var(--duration-300) var(--ease-out);
}

.animate-slide-up {
  animation: slideUp var(--duration-300) var(--ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--duration-200) var(--ease-out);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Gradient Utilities */
.gradient-coral-teal {
  background: linear-gradient(135deg, var(--coral-500), var(--teal-500));
}

.gradient-orange-purple {
  background: linear-gradient(135deg, var(--orange-500), var(--purple-500));
}

.gradient-text-coral-teal {
  background: linear-gradient(135deg, var(--coral-500), var(--teal-500));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass Effect Utilities */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid oklch(from var(--border-light) l c h / 0.5);
}

.glass-strong {
  background: oklch(from var(--bg-glass) l c h / 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid oklch(from var(--border-medium) l c h / 0.7);
}

/* Stagger Animations for Lists */
.stagger-children > * {
  animation: slideUp var(--duration-300) ease-out;
}

.stagger-children > *:nth-child(1) { animation-delay: 0ms; }
.stagger-children > *:nth-child(2) { animation-delay: 50ms; }
.stagger-children > *:nth-child(3) { animation-delay: 100ms; }
.stagger-children > *:nth-child(4) { animation-delay: 150ms; }
.stagger-children > *:nth-child(5) { animation-delay: 200ms; }
.stagger-children > *:nth-child(6) { animation-delay: 250ms; }
.stagger-children > *:nth-child(7) { animation-delay: 300ms; }
.stagger-children > *:nth-child(8) { animation-delay: 350ms; }

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
