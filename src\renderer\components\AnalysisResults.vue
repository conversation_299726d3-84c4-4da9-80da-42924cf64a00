<template>
  <div class="analysis-results">
    <!-- Results Header -->
    <ResultsHeader
      :results-count="results.length"
      :total-resources="totalResources"
      @export="handleExport"
    />

    <!-- Filters and Search -->
    <ResultsFilters
      :search-query="searchQuery"
      :selected-type-filter="selectedTypeFilter"
      :selected-file-filter="selectedFileFilter"
      :available-types="availableTypes"
      :available-files="availableFiles"
      @update-search="searchQuery = $event"
      @update-type-filter="selectedTypeFilter = $event"
      @update-file-filter="selectedFileFilter = $event"
      @clear-filters="clearFilters"
    />

    <!-- Results by File -->
    <div class="results-content">
      <FileResultCard
        v-for="result in filteredResults"
        :key="result.filePath"
        :result="result"
        :is-expanded="expandedFiles.has(result.filePath)"
        :filtered-resources-count="getFilteredResourcesForFile(result).length"
        @toggle-expanded="toggleFileExpanded(result.filePath)"
      >
        <template #resources-table>

          <ResourcesTable
            :resources="getSortedResourcesForFile(result)"
            :sort-field="sortField"
            :sort-direction="sortDirection"
            @sort-change="handleSortChange"
          />
        </template>
      </FileResultCard>
    </div>

    <!-- Empty State -->
    <EmptyState
      v-if="filteredResults.length === 0 && results.length > 0"
      message="No resources match your current filters."
      :show-clear-button="true"
      @clear-filters="clearFilters"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps } from 'vue';
import type { AnalyzedPackage, ResourceInfo } from '../../types/analysis';

// Import subcomponents
import ResultsHeader from './analysis/ResultsHeader.vue';
import ResultsFilters from './analysis/ResultsFilters.vue';
import FileResultCard from './analysis/FileResultCard.vue';
import ResourcesTable from './analysis/ResourcesTable.vue';
import EmptyState from './analysis/EmptyState.vue';

// Import utilities
import { useFileDownload, useResourceFiltering } from '../composables/useAnalysisUtils';

const props = defineProps<{
  results: AnalyzedPackage[];
}>();

// Utilities
const { exportAsJSON, exportAsCSV } = useFileDownload();
const { filterResources, getAvailableTypes, getAvailableFiles } = useResourceFiltering();

// Reactive state
const searchQuery = ref('');
const selectedTypeFilter = ref('');
const selectedFileFilter = ref('');
const sortField = ref<keyof ResourceInfo>('key');
const sortDirection = ref<'asc' | 'desc'>('asc');
const expandedFiles = ref(new Set<string>());

// Computed properties
const totalResources = computed(() =>
  props.results.reduce((total, result) => total + result.resources.length, 0)
);

const availableTypes = computed(() => getAvailableTypes(props.results));
const availableFiles = computed(() => getAvailableFiles(props.results));

const hasActiveFilters = computed(() =>
  searchQuery.value || selectedTypeFilter.value || selectedFileFilter.value
);

const filteredResults = computed(() => {
  let filtered = props.results;
  
  // Filter by selected file
  if (selectedFileFilter.value) {
    filtered = filtered.filter(result => result.filePath === selectedFileFilter.value);
  }
  
  // Filter by search query or type
  if (searchQuery.value || selectedTypeFilter.value) {
    filtered = filtered.map(result => ({
      ...result,
      resources: result.resources.filter(resource => {
        const matchesSearch = !searchQuery.value || 
          resource.key.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          resource.type.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
          resource.contentSnippet.toLowerCase().includes(searchQuery.value.toLowerCase());
          
        const matchesType = !selectedTypeFilter.value || 
          resource.type === selectedTypeFilter.value;
          
        return matchesSearch && matchesType;
      })
    })).filter(result => result.resources.length > 0);
  }
  
  return filtered;
});

// Methods
function getFilteredResourcesForFile(result: AnalyzedPackage): ResourceInfo[] {
  return filterResources(result.resources, searchQuery.value, selectedTypeFilter.value);
}

function getSortedResourcesForFile(result: AnalyzedPackage): ResourceInfo[] {
  const resources = getFilteredResourcesForFile(result);

  return [...resources].sort((a, b) => {
    let aVal = a[sortField.value];
    let bVal = b[sortField.value];

    // Handle numeric sorting for group
    if (sortField.value === 'group') {
      aVal = Number(aVal);
      bVal = Number(bVal);
    }

    // Convert to string for comparison
    const aStr = String(aVal).toLowerCase();
    const bStr = String(bVal).toLowerCase();

    const comparison = aStr.localeCompare(bStr, undefined, { numeric: true });
    return sortDirection.value === 'asc' ? comparison : -comparison;
  });
}

// Event handlers
function handleExport(format: 'json' | 'csv') {
  if (format === 'json') {
    exportAsJSON(filteredResults.value);
  } else {
    exportAsCSV(filteredResults.value);
  }
}

function handleSortChange(field: keyof ResourceInfo, direction: 'asc' | 'desc') {
  sortField.value = field;
  sortDirection.value = direction;
}

function toggleFileExpanded(filePath: string) {
  if (expandedFiles.value.has(filePath)) {
    expandedFiles.value.delete(filePath);
  } else {
    expandedFiles.value.add(filePath);
  }
}

function clearFilters() {
  searchQuery.value = '';
  selectedTypeFilter.value = '';
  selectedFileFilter.value = '';
}



// Auto-expand first file when results are loaded
if (props.results.length > 0) {
  expandedFiles.value.add(props.results[0].filePath);
}
</script>

<style scoped>
/* Main container styles only - subcomponent styles are in their respective files */
.analysis-results {
  /* Any main container specific styles can go here */
}
</style>