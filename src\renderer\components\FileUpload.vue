<template>
  <div class="file-upload">
    <div
      class="drop-zone"
      :class="{ 'drop-zone-active': isDragOver }"
      @click="triggerFileInput"
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
    >
      <div class="drop-zone-content">
        <div class="drop-zone-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        </div>
        <h3 class="drop-zone-title">Drop your .package files here</h3>
        <p class="drop-zone-subtitle text-muted">
          or <span class="text-primary font-medium">click to browse</span>
        </p>
        <div class="drop-zone-formats text-sm text-muted mt-sm">
          Supports: .package, .ts4script files
        </div>
      </div>
    </div>
    
    <input
      ref="fileInput"
      type="file"
      multiple
      accept=".package,.ts4script"
      @change="handleFileSelect"
      style="display: none;"
    />
    
    <!-- File list -->
    <div v-if="selectedFiles.length > 0" class="selected-files mt-lg">
      <h4 class="font-medium mb-md">Selected Files ({{ selectedFiles.length }})</h4>
      <div class="file-list">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="file-item"
        >
          <div class="file-info">
            <div class="file-name font-medium">{{ file.name }}</div>
            <div class="file-details text-sm text-muted">
              {{ formatFileSize(file.size) }} • {{ getFileType(file.name) }}
            </div>
          </div>
          <button
            class="btn btn-sm btn-secondary"
            @click="removeFile(index)"
            title="Remove file"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
      </div>
      
      <div class="file-actions mt-md">
        <button
          class="btn btn-primary"
          @click="analyzeFiles"
          :disabled="isAnalyzing"
        >
          <div v-if="isAnalyzing" class="loading"></div>
          {{ isAnalyzing ? 'Analyzing...' : 'Analyze Files' }}
        </button>
        <button
          class="btn btn-secondary"
          @click="clearFiles"
          :disabled="isAnalyzing"
        >
          Clear All
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineEmits } from 'vue';

// Props and emits
const emit = defineEmits<{
  filesSelected: [files: File[]];
  analyzeRequested: [files: File[]];
}>();

// Reactive state
const fileInput = ref<HTMLInputElement>();
const selectedFiles = ref<File[]>([]);
const isDragOver = ref(false);
const isAnalyzing = ref(false);

// Drag and drop handlers
function handleDragOver(event: DragEvent) {
  event.preventDefault();
  event.stopPropagation();
}

function handleDragEnter(event: DragEvent) {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = true;
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault();
  event.stopPropagation();
  // Only set to false if we're leaving the drop zone entirely
  if (!event.currentTarget?.contains(event.relatedTarget as Node)) {
    isDragOver.value = false;
  }
}

function handleDrop(event: DragEvent) {
  event.preventDefault();
  event.stopPropagation();
  isDragOver.value = false;
  
  const files = Array.from(event.dataTransfer?.files || []);
  const validFiles = files.filter(isValidFile);
  
  if (validFiles.length > 0) {
    addFiles(validFiles);
  }
}

// File selection handlers
function triggerFileInput() {
  fileInput.value?.click();
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    const files = Array.from(target.files);
    const validFiles = files.filter(isValidFile);
    addFiles(validFiles);
  }
}

// File management
function addFiles(files: File[]) {
  // Add new files, avoiding duplicates
  const newFiles = files.filter(file => 
    !selectedFiles.value.some(existing => 
      existing.name === file.name && existing.size === file.size
    )
  );
  
  selectedFiles.value.push(...newFiles);
  emit('filesSelected', selectedFiles.value);
}

function removeFile(index: number) {
  selectedFiles.value.splice(index, 1);
  emit('filesSelected', selectedFiles.value);
}

function clearFiles() {
  selectedFiles.value = [];
  if (fileInput.value) {
    fileInput.value.value = '';
  }
  emit('filesSelected', selectedFiles.value);
}

function analyzeFiles() {
  if (selectedFiles.value.length > 0) {
    isAnalyzing.value = true;
    emit('analyzeRequested', selectedFiles.value);
    
    // Reset analyzing state after a delay (will be controlled by parent)
    setTimeout(() => {
      isAnalyzing.value = false;
    }, 1000);
  }
}

// Utility functions
function isValidFile(file: File): boolean {
  const validExtensions = ['.package', '.ts4script'];
  return validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileType(filename: string): string {
  if (filename.toLowerCase().endsWith('.package')) {
    return 'Package File';
  } else if (filename.toLowerCase().endsWith('.ts4script')) {
    return 'Script File';
  }
  return 'Unknown';
}

// Expose methods for parent component
defineExpose({
  clearFiles,
  isAnalyzing
});
</script>

<style scoped>
/* Import design system */
@import '../styles/simonitor-design-system.css';

.file-upload {
  width: 100%;
}

.drop-zone {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.drop-zone-content {
  text-align: center;
}

.drop-zone-icon {
  color: var(--text-muted);
  margin-bottom: var(--space-4);
}

.drop-zone-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin: 0 0 var(--space-2) 0;
  color: var(--text-primary);
}

.drop-zone-subtitle {
  margin: 0;
}

.selected-files {
  border-top: 1px solid var(--border-light);
  padding-top: var(--space-6);
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background-color: var(--bg-secondary);
}

.file-info {
  flex: 1;
}

.file-name {
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.file-actions {
  display: flex;
  gap: var(--space-2);
}

.text-primary {
  color: var(--primary);
}
</style>