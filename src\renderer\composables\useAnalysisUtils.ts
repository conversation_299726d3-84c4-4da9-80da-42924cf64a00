/**
 * Analysis Results Utility Functions
 * Shared utilities for analysis components following Vue 3 Composition API best practices
 */

import type { AnalyzedPackage, ResourceInfo } from '../../types/analysis';

/**
 * File download utility for exporting analysis results
 */
export function useFileDownload() {
  function downloadFile(content: string, filename: string, mimeType: string) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  function exportAsJSON(data: any, filename: string = 'simonitor-analysis.json') {
    const dataStr = JSON.stringify(data, null, 2);
    downloadFile(dataStr, filename, 'application/json');
  }

  function exportAsCSV(results: AnalyzedPackage[], filename: string = 'simonitor-analysis.csv') {
    const headers = ['File', 'Resource Key', 'Type', 'Group', 'Instance', 'Is Override', 'Content'];
    const rows = [headers];
    
    results.forEach(result => {
      result.resources.forEach(resource => {
        rows.push([
          getFileName(result.filePath),
          resource.key,
          resource.type,
          resource.group.toString(),
          resource.instance,
          resource.isOverride.toString(),
          resource.contentSnippet
        ]);
      });
    });
    
    const csvContent = rows.map(row => 
      row.map(cell => `"${cell.replace(/"/g, '""')}"`).join(',')
    ).join('\n');
    
    downloadFile(csvContent, filename, 'text/csv');
  }

  return {
    downloadFile,
    exportAsJSON,
    exportAsCSV
  };
}

/**
 * File name extraction utility
 */
export function getFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || filePath;
}

/**
 * File size formatting utility
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Category display name formatting
 */
export function getCategoryDisplayName(category: string): string {
  const displayNames: Record<string, string> = {
    'cas_cc': 'CAS Custom Content',
    'build_buy_cc': 'Build/Buy Custom Content',
    'script_mod': 'Script Mod',
    'tuning_mod': 'Tuning Mod',
    'override': 'Override',
    'framework': 'Framework',
    'library': 'Library',
    'unknown': 'Unknown'
  };
  
  return displayNames[category] || category;
}

/**
 * Subcategory formatting utility
 */
export function formatSubcategory(subcategory: string): string {
  return subcategory
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Number formatting utility
 */
export function formatNumber(num: number): string {
  return num.toLocaleString();
}

/**
 * Deep analysis detection utility
 */
export function hasDeepAnalysis(result: AnalyzedPackage): boolean {
  return !!(result.metadata?.deepAnalysis && (
    result.metadata.deepAnalysis.casInfo ||
    result.metadata.deepAnalysis.objectInfo ||
    result.metadata.deepAnalysis.scriptInfo ||
    result.metadata.deepAnalysis.detailedDescription !== 'Unknown content' ||
    (result.metadata.deepAnalysis.suggestedTags && result.metadata.deepAnalysis.suggestedTags.length > 0)
  ));
}

/**
 * Resource filtering utility
 */
export function useResourceFiltering() {
  function filterResources(
    resources: ResourceInfo[], 
    searchQuery: string, 
    typeFilter: string
  ): ResourceInfo[] {
    return resources.filter(resource => {
      const matchesSearch = !searchQuery || 
        resource.key.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
        resource.contentSnippet.toLowerCase().includes(searchQuery.toLowerCase());
        
      const matchesType = !typeFilter || resource.type === typeFilter;
        
      return matchesSearch && matchesType;
    });
  }

  function getAvailableTypes(results: AnalyzedPackage[]): string[] {
    const types = new Set<string>();
    results.forEach(result => {
      result.resources.forEach(resource => {
        types.add(resource.type);
      });
    });
    return Array.from(types).sort();
  }

  function getAvailableFiles(results: AnalyzedPackage[]): string[] {
    return results.map(result => result.filePath).sort();
  }

  return {
    filterResources,
    getAvailableTypes,
    getAvailableFiles
  };
}
