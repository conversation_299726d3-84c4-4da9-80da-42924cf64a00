<template>
  <div class="results-header card-header">
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-xl font-semibold mb-sm">Analysis Results</h2>
        <div class="results-summary text-sm text-muted">
          <span class="font-medium">{{ resultsCount }}</span> file(s) analyzed • 
          <span class="font-medium">{{ totalResources }}</span> total resources
        </div>
      </div>
      <div class="results-actions flex gap-sm">
        <button 
          class="btn btn-secondary btn-sm" 
          @click="handleExport('json')"
          :disabled="resultsCount === 0"
        >
          Export JSON
        </button>
        <button 
          class="btn btn-secondary btn-sm" 
          @click="handleExport('csv')"
          :disabled="resultsCount === 0"
        >
          Export CSV
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// Props
interface Props {
  resultsCount: number;
  totalResources: number;
}

const props = defineProps<Props>();

// Events
interface Emits {
  (e: 'export', format: 'json' | 'csv'): void;
}

const emit = defineEmits<Emits>();

// Methods
function handleExport(format: 'json' | 'csv') {
  emit('export', format);
}
</script>

<style scoped>
.results-header {
  margin-bottom: var(--spacing-lg);
}

.results-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
</script>
