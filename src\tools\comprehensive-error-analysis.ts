#!/usr/bin/env tsx

/**
 * Comprehensive Error Analysis Tool for Simonitor Auto-Scan
 * Runs auto-scan with comprehensive error logging and analysis
 */

import * as fs from 'fs';
import * as path from 'path';
import { ErrorAnalysisService, ErrorCategory, ErrorSeverity } from '../services/analysis/ErrorAnalysisService';

// Import analysis services
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';
import { ThumbnailExtractionService } from '../services/visual/ThumbnailExtractionService';

const MODS_FOLDER = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';

/**
 * Analyze mods folder with comprehensive error logging
 */
async function analyzeModsFolderWithErrorLogging(
    folderPath: string,
    maxFiles: number = 50 // Start with limited set for analysis
): Promise<void> {
    const errorAnalysis = ErrorAnalysisService.getInstance();
    const analysisService = new PackageAnalysisService();
    
    console.log(`🔍 Starting comprehensive error analysis on: ${folderPath}`);
    console.log(`📊 Analyzing up to ${maxFiles} files for error patterns`);
    
    // Start error logging
    errorAnalysis.startLogging();
    
    try {
        // Get all mod files
        const modFiles = await getModFiles(folderPath, maxFiles);
        console.log(`📁 Found ${modFiles.length} mod files to analyze`);
        
        let processedCount = 0;
        const startTime = Date.now();
        
        for (const filePath of modFiles) {
            try {
                console.log(`\n🔍 [${processedCount + 1}/${modFiles.length}] Analyzing: ${path.basename(filePath)}`);
                
                // Read file buffer
                const buffer = await fs.promises.readFile(filePath);
                
                // Perform detailed analysis (this will trigger various error scenarios)
                const analysisResult = await analysisService.detailedAnalyzeAsync(buffer, filePath);
                
                // Attempt thumbnail extraction (this often triggers DXT/S4TK errors)
                try {
                    const thumbnailResult = await ThumbnailExtractionService.extractThumbnails(
                        buffer,
                        path.basename(filePath),
                        {
                            maxThumbnails: 10,
                            preferredFormat: 'webp',
                            maxWidth: 256,
                            maxHeight: 256,
                            prioritizeCasThumbnails: true,
                            generateFallbacks: true,
                            quality: 85
                        }
                    );
                    
                    if (!thumbnailResult.success && thumbnailResult.errors.length > 0) {
                        for (const error of thumbnailResult.errors) {
                            errorAnalysis.logError(new Error(error), {
                                fileName: path.basename(filePath),
                                operation: 'thumbnail_extraction_batch',
                                additionalData: {
                                    bufferSize: buffer.length,
                                    totalImagesFound: thumbnailResult.totalImagesFound
                                }
                            });
                        }
                    }
                } catch (thumbnailError) {
                    errorAnalysis.logError(thumbnailError instanceof Error ? thumbnailError : new Error(String(thumbnailError)), {
                        fileName: path.basename(filePath),
                        operation: 'thumbnail_extraction_batch',
                        additionalData: { bufferSize: buffer.length }
                    });
                }
                
                processedCount++;
                
                // Progress update
                if (processedCount % 10 === 0) {
                    const elapsed = (Date.now() - startTime) / 1000;
                    const rate = processedCount / elapsed;
                    console.log(`📊 Progress: ${processedCount}/${modFiles.length} (${rate.toFixed(1)} files/sec)`);
                }
                
            } catch (fileError) {
                errorAnalysis.logError(fileError instanceof Error ? fileError : new Error(String(fileError)), {
                    fileName: path.basename(filePath),
                    operation: 'file_processing',
                    additionalData: { filePath }
                });
                processedCount++;
            }
        }
        
        console.log(`\n✅ Completed analysis of ${processedCount} files`);
        
    } catch (error) {
        console.error('❌ Error during comprehensive analysis:', error);
        errorAnalysis.logError(error instanceof Error ? error : new Error(String(error)), {
            operation: 'comprehensive_analysis',
            additionalData: { folderPath, maxFiles }
        });
    }
    
    // Stop logging and generate report
    const report = errorAnalysis.stopLogging();
    
    // Display comprehensive analysis results
    displayAnalysisResults(report);
}

/**
 * Get mod files from folder
 */
async function getModFiles(folderPath: string, maxFiles: number): Promise<string[]> {
    const modFiles: string[] = [];
    
    async function scanDirectory(dirPath: string): Promise<void> {
        if (modFiles.length >= maxFiles) return;
        
        try {
            const items = await fs.promises.readdir(dirPath, { withFileTypes: true });
            
            for (const item of items) {
                if (modFiles.length >= maxFiles) break;
                
                const fullPath = path.join(dirPath, item.name);
                
                if (item.isDirectory()) {
                    await scanDirectory(fullPath);
                } else if (item.isFile()) {
                    const ext = path.extname(item.name).toLowerCase();
                    if (ext === '.package' || ext === '.ts4script') {
                        modFiles.push(fullPath);
                    }
                }
            }
        } catch (error) {
            console.warn(`Warning: Could not scan directory ${dirPath}:`, error);
        }
    }
    
    await scanDirectory(folderPath);
    return modFiles;
}

/**
 * Display comprehensive analysis results
 */
function displayAnalysisResults(report: any): void {
    console.log('\n' + '='.repeat(80));
    console.log('📊 COMPREHENSIVE ERROR ANALYSIS REPORT');
    console.log('='.repeat(80));
    
    console.log(`\n📈 SESSION SUMMARY:`);
    console.log(`   Total Unique Errors: ${report.sessionInfo.totalErrors}`);
    console.log(`   Total Error Occurrences: ${report.sessionInfo.totalOccurrences}`);
    
    console.log(`\n🏷️  ERRORS BY CATEGORY:`);
    for (const [category, errors] of Object.entries(report.errorsByCategory)) {
        const errorArray = errors as any[];
        const totalOccurrences = errorArray.reduce((sum, e) => sum + e.frequency, 0);
        console.log(`   ${category}: ${errorArray.length} unique errors (${totalOccurrences} occurrences)`);
    }
    
    console.log(`\n⚠️  ERRORS BY SEVERITY:`);
    for (const [severity, errors] of Object.entries(report.errorsBySeverity)) {
        const errorArray = errors as any[];
        const totalOccurrences = errorArray.reduce((sum, e) => sum + e.frequency, 0);
        console.log(`   ${severity.toUpperCase()}: ${errorArray.length} unique errors (${totalOccurrences} occurrences)`);
    }
    
    console.log(`\n🔥 TOP 10 MOST FREQUENT ERRORS:`);
    report.topErrors.slice(0, 10).forEach((error: any, index: number) => {
        console.log(`   ${index + 1}. [${error.severity.toUpperCase()}] ${error.message}`);
        console.log(`      Category: ${error.category} | Frequency: ${error.frequency}`);
        if (error.fileName) {
            console.log(`      Example File: ${error.fileName}`);
        }
        console.log('');
    });
    
    console.log(`\n💡 RECOMMENDATIONS:`);
    report.recommendations.forEach((rec: string, index: number) => {
        console.log(`   ${index + 1}. ${rec}`);
    });
    
    console.log('\n' + '='.repeat(80));
    console.log('📝 Detailed report saved to logs directory');
    console.log('='.repeat(80));
}

/**
 * Main execution
 */
async function main(): Promise<void> {
    try {
        // Check if mods folder exists
        if (!fs.existsSync(MODS_FOLDER)) {
            console.error(`❌ Mods folder not found: ${MODS_FOLDER}`);
            process.exit(1);
        }
        
        // Run comprehensive error analysis on FULL collection
        await analyzeModsFolderWithErrorLogging(MODS_FOLDER, 2000); // Analyze up to 2000 files (full collection)
        
    } catch (error) {
        console.error('❌ Fatal error during analysis:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}

export { analyzeModsFolderWithErrorLogging };
