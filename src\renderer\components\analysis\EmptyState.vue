<template>
  <div class="empty-state text-center py-xl">
    <div class="text-muted">
      <svg 
        width="48" 
        height="48" 
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        stroke-width="1.5" 
        class="mb-md"
      >
        <circle cx="11" cy="11" r="8"/>
        <path d="m21 21-4.35-4.35"/>
      </svg>
      <p>{{ message }}</p>
      <button 
        v-if="showClearButton"
        class="btn btn-secondary btn-sm mt-sm" 
        @click="handleClearFilters"
      >
        Clear Filters
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue';

// Props
interface Props {
  message?: string;
  showClearButton?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  message: 'No resources match your current filters.',
  showClearButton: true
});

// Events
interface Emits {
  (e: 'clear-filters'): void;
}

const emit = defineEmits<Emits>();

// Methods
function handleClearFilters() {
  emit('clear-filters');
}
</script>

<style scoped>
.empty-state svg {
  color: var(--text-muted);
  margin: 0 auto;
}
</style>
</script>
