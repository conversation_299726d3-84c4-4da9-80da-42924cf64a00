{"systemValidation": {"configurationLoaded": true, "componentsWorking": true, "fileTypeSupport": true, "sampleTestPassed": true, "issues": []}, "contentAnalysis": {"totalAnalyzed": 100, "successRate": 1, "contentTypeBreakdown": {"unknown": 11, "cas_only": 17, "objects_only": 40, "script_mod": 7, "gameplay_only": 11, "mixed_content": 14}, "confidenceScores": [0, 30, 70, 0.9, 65, 65, 65, 65, 65, 65, 65, 65, 65, 30, 0, 30, 70, 70, 100, 100, 100, 100, 30, 100, 100, 100, 70, 30, 100, 100, 70, 30, 70, 30, 70, 0, 70, 0, 0, 0, 0, 65, 0, 0, 0.9, 70, 70, 100, 70, 95, 30, 0.9, 30, 30, 70, 0.9, 0, 0.9, 70, 70, 65, 70, 70, 70, 70, 70, 70, 100, 70, 70, 70, 70, 70, 30, 70, 70, 70, 70, 70, 70, 0.9, 30, 30, 70, 30, 100, 70, 70, 0, 30, 100, 30, 40, 70, 100, 70, 70, 70, 70, 70], "spotCheckResults": [{"fileName": "Aurum_HairstyleFMM_146_Lotus.package", "expectedType": "cas_only", "detectedType": "cas_only", "confidence": 30, "correct": true}, {"fileName": "brazenlotus_ModCORE.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "GROVE_Accent_Armchair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "GROVE_Modern_Armchair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "LittleMsSam_SpendWeekendWith.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "LIVIN'RUM_Marble_Accent_Chair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "LIVIN'RUM_Shelf2.package", "expectedType": "object_only", "detectedType": "objects_only", "confidence": 70, "correct": false}, {"fileName": "mc_cheats.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "miiko-3d-eyelashes-part-1(right-lip-mole).package", "expectedType": "cas_only", "detectedType": "cas_only", "confidence": 30, "correct": true}, {"fileName": "Meerigold_EmployedeKonbini.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "Meerigold_Soinsenmaisonderepos.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "SIXAMcc_BohoBaby-Confort-Chair.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}, {"fileName": "SIXAMcc_BohoBathroom-Storage-ToiletStorage-Opened.package", "expectedType": "object_only", "detectedType": "objects_only", "confidence": 70, "correct": false}, {"fileName": "SonyaSims 211 Hair Cappuccino KIDS.package", "expectedType": "cas_only", "detectedType": "cas_only", "confidence": 30, "correct": true}, {"fileName": "thepancake1_CuteRomance_v6j.ts4script", "expectedType": "script_mod", "detectedType": "script_mod", "confidence": 0.9, "correct": true}, {"fileName": "UnderStairsStorage_Medium_2.package", "expectedType": "object_only", "detectedType": "objects_only", "confidence": 70, "correct": false}, {"fileName": "[<PERSON>]Simple Live - Beanbag Chair#2.package", "expectedType": "cas_only", "detectedType": "mixed_content", "confidence": 100, "correct": false}]}, "fullCollection": {"totalMods": 1336, "packageFiles": 1271, "scriptFiles": 65, "successfulAnalyses": 1326, "failedAnalyses": 10, "totalProcessingTime": 71190, "averageTimePerMod": 53.285928143712574, "memoryUsage": {"peak": 195.03562927246094, "average": 71.92351969987332, "final": 98.95856475830078}, "errorSummary": {"corruptedFiles": [], "analysisFailures": ["LittleMsSam_AutoGardening_Addon_MoistureWeedInfestationDecay.package", "LittleMsSam_AutoGardening_Addon_NoPuddlesOutside.package", "LittleMsSam_Babysitter.package", "LittleMsSam_CanIComeOver.package", "LittleMsSam_Chores.package", "LittleMsSam_<PERSON>res_Addon_NotOnDuty.package", "LittleMsSam_Chores_Addon_UniversityBoard.package", "LittleMsSam_RomanticMassage.package", "thepancake1-MoreTraitsInCAS-v1t-1.112.package", "XmlInjector_Test_v4.2.package"], "memoryIssues": [], "otherErrors": []}}, "conflictDetection": {"knownConflicts": 0, "resourceConflicts": 1, "falsePositives": 0, "evidenceBasedAccuracy": true, "detectedIssues": []}, "organization": {"organizationScore": 90, "recommendations": ["Large collection detected - consider using mod management tools"], "optimizationOpportunities": ["Regular cleanup of unused mods", "Backup important mod configurations"], "collectionHealth": "excellent"}, "performance": {"overallScore": "excellent", "scalabilityAssessment": "good", "memoryEfficiency": "excellent", "processingSpeed": "excellent", "recommendations": []}}