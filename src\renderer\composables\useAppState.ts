/**
 * Vue 3 Composable: Application State Management
 * Extracts complex application-level state from App.vue for better organization
 * Follows Vue 3 Composition API best practices with proper reactivity patterns
 */

import { ref, watch, nextTick, type Ref } from 'vue';
import type { AnalyzedPackage } from '../../types/analysis';

// Application state interface
interface AppState {
  selectedFiles: File[];
  analysisResults: any[];
  isAnalyzing: boolean;
  analyzedCount: number;
  analysisError: string | null;
  isSettingsOpen: boolean;
  currentModsFolder: string;
}

// Utility functions for file processing
const getFileName = (filePath: string): string => {
  return filePath.split(/[\\/]/).pop() || 'Unknown';
};

const getFileExtension = (filePath: string): string => {
  const fileName = getFileName(filePath);
  const lastDot = fileName.lastIndexOf('.');
  return lastDot > 0 ? fileName.substring(lastDot) : '.package';
};

export function useAppState() {
  // Reactive state
  const selectedFiles = ref<File[]>([]);
  const analysisResults = ref<any[]>([]);
  const isAnalyzing = ref(false);
  const analyzedCount = ref(0);
  const analysisError = ref<string | null>(null);
  const isSettingsOpen = ref(false);
  const currentModsFolder = ref<string>('');

  // Watch for analysis results changes to trigger reactivity
  watch(analysisResults, (newResults, oldResults) => {
    // Analysis results changed - trigger reactivity
    console.log('📊 [useAppState] Analysis results updated:', newResults.length, 'mods');
  }, { immediate: true, deep: true });

  // Watch for analyzing state changes
  watch(isAnalyzing, (newAnalyzing, oldAnalyzing) => {
    // Loading state changed - trigger reactivity
    console.log('⏳ [useAppState] Analysis state changed:', newAnalyzing ? 'Started' : 'Finished');
  }, { immediate: true });

  // State management methods
  const clearError = () => {
    analysisError.value = null;
  };

  const resetAnalysisState = () => {
    selectedFiles.value = [];
    analysisResults.value = [];
    analyzedCount.value = 0;
    analysisError.value = null;
    isAnalyzing.value = false;
  };

  const handleFilesSelected = (files: File[]) => {
    selectedFiles.value = files;
    // Clear previous results when new files are selected
    if (files.length === 0) {
      analysisResults.value = [];
      analysisError.value = null;
    }
  };

  const startAnalysis = () => {
    isAnalyzing.value = true;
    analyzedCount.value = 0;
    analysisResults.value = [];
    analysisError.value = null;
  };

  const finishAnalysis = () => {
    isAnalyzing.value = false;
  };

  const setAnalysisError = (error: string) => {
    analysisError.value = error;
    isAnalyzing.value = false;
  };

  // Process analysis results with proper data mapping
  const processAnalysisResults = async (analysisResult: any) => {
    try {
      // Check if data exists and is an array
      if (analysisResult.data && Array.isArray(analysisResult.data)) {
        const processedResults = analysisResult.data
          .filter((result: any) => result && (result.filePath || result.fileName)) // Filter out invalid results
          .map((result: any) => ({
            ...result,
            // Map filePath to fileName for UI compatibility
            fileName: result.filePath ? getFileName(result.filePath) : result.fileName || 'Unknown',
            // Ensure fileExtension is present
            fileExtension: result.fileExtension || (result.filePath ? getFileExtension(result.filePath) : '.package'),
            // Ensure fileSize is present
            fileSize: result.fileSize || 0,
            // Ensure all required properties for the UI
            resourceIntelligenceData: result.intelligence?.resourceIntelligence,
            dependencyData: result.intelligence?.dependencies,
            qualityAssessmentData: result.intelligence?.qualityAssessment,
            metadataConfidence: result.metadataConfidence || 0,
            processingTime: result.processingTime || 0,
            resourceCount: result.resourceCount || 0,
            // Add quality score from intelligence analysis
            qualityScore: result.intelligence?.qualityAssessment?.overallScore || 0,
            // Ensure metadata properties exist with safe fallbacks
            author: result.metadata?.author || result.author || 'Unknown',
            version: result.metadata?.version || result.version || '1.0',
            modName: result.metadata?.modName || result.modName || (result.fileName || 'Unknown Mod'),
            description: result.metadata?.description || result.description || 'No description available',
            // Ensure thumbnails array exists
            thumbnails: result.thumbnails || [],
            primaryThumbnail: result.primaryThumbnail || null,
            // Add any additional UI-required properties
            isSelected: false,
            isExpanded: false,
          }));

        console.log('✅ [useAppState] Processed analysis results:', processedResults.length, 'mods');
        
        analysisResults.value = processedResults;

        // Force Vue to re-render
        await nextTick();

        // Show warning if there were partial errors but still got some data
        if (!analysisResult.success && analysisResults.value.length > 0) {
          analysisError.value = `Analysis completed with some errors. Showing ${analysisResults.value.length} successfully analyzed mods. Check console for details.`;
        }
      } else {
        analysisError.value = 'No valid mod data found. Check that the folder contains .package or .ts4script files.';
      }
    } catch (error) {
      console.error('❌ [useAppState] Error processing analysis results:', error);
      analysisError.value = 'Error processing analysis results. Check console for details.';
    }
  };

  // Settings management
  const openSettings = () => {
    isSettingsOpen.value = true;
  };

  const closeSettings = () => {
    isSettingsOpen.value = false;
  };

  const toggleSettings = () => {
    isSettingsOpen.value = !isSettingsOpen.value;
  };

  return {
    // State
    selectedFiles,
    analysisResults,
    isAnalyzing,
    analyzedCount,
    analysisError,
    isSettingsOpen,
    currentModsFolder,

    // Methods
    clearError,
    resetAnalysisState,
    handleFilesSelected,
    startAnalysis,
    finishAnalysis,
    setAnalysisError,
    processAnalysisResults,
    openSettings,
    closeSettings,
    toggleSettings,
  };
}
