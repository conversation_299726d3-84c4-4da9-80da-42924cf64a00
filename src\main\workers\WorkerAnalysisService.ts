/**
 * Worker-Safe Analysis Service
 * A stripped-down version of PackageAnalysisService that can run in worker threads
 * without importing Electron APIs or other main-process dependencies
 */

import { ModCategory, FileType } from '../../types/analysis';
import { quickAnalysisService } from '../../services/analysis/core/QuickAnalysisService';
import { detailedAnalysisService } from '../../services/analysis/core/DetailedAnalysisService';
import type {
    EnhancedDetailedAnalysisResult,
    CancellationToken
} from '../../types/analysis-results';

/**
 * Worker-safe analysis service that only includes core analysis functionality
 * without any Electron dependencies or cache services
 */
export class WorkerAnalysisService {
    
    /**
     * Performs detailed analysis in worker thread context
     * This is the main method called by worker threads
     */
    public async detailedAnalyzeAsync(
        buffer: Buffer, 
        filePath: string, 
        cancellationToken?: CancellationToken
    ): Promise<EnhancedDetailedAnalysisResult> {
        try {
            // Check cancellation before starting
            if (cancellationToken?.isCancelled) {
                throw new Error('Analysis cancelled');
            }

            // Perform quick analysis first
            const quickResult = await quickAnalysisService.analyzeAsync(buffer, filePath, cancellationToken);
            
            // Check cancellation after quick analysis
            if (cancellationToken?.isCancelled) {
                throw new Error('Analysis cancelled');
            }

            // Handle validation issues
            if (quickResult.validationIssues.length > 0) {
                return this.createErrorResult(
                    buffer, 
                    filePath, 
                    quickResult.validationIssues.join(', ')
                );
            }

            // Check cancellation before detailed analysis
            if (cancellationToken?.isCancelled) {
                throw new Error('Analysis cancelled');
            }

            // Perform detailed analysis
            const result = await detailedAnalysisService.analyzeAsync(
                buffer, 
                filePath, 
                quickResult, 
                cancellationToken
            );

            return result;
        } catch (error) {
            // Handle cancellation gracefully
            if (cancellationToken?.isCancelled || error instanceof Error && error.message.includes('cancelled')) {
                throw error;
            }

            // Create error result for other failures
            const errorMessage = error instanceof Error ? error.message : String(error);
            return this.createErrorResult(buffer, filePath, errorMessage);
        }
    }

    /**
     * Creates an error result when analysis fails
     */
    private createErrorResult(
        buffer: Buffer, 
        filePath: string, 
        errorMessage: string
    ): EnhancedDetailedAnalysisResult {
        return {
            filePath,
            fileSize: buffer.length,
            fileType: FileType.UNKNOWN,
            category: ModCategory.UNKNOWN,
            subcategory: 'error',
            resourceCount: 0,
            isOverride: false,
            resources: [],
            dependencies: [],
            conflicts: [],
            metadata: { error: errorMessage },
            specializedResources: [],
            resourceValidation: { isValid: false, issues: [errorMessage] },
            performanceMetrics: { 
                totalTime: 0, 
                quickAnalysisTime: 0, 
                detailedAnalysisTime: 0, 
                resourceCount: 0 
            },
            extractedItemNames: [],
            metadataConfidence: 0,
            hasStringTable: false,
        } as EnhancedDetailedAnalysisResult;
    }
}
