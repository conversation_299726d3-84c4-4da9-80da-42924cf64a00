/**
 * Memory-Safe DDS Processor
 * Fixes S4TK memory allocation issues by implementing custom DDS processing
 * that doesn't hit Emscripten memory limits
 */

import { DdsImage } from '@s4tk/models';

export interface DdsProcessingOptions {
    maxMemoryUsage: number; // bytes
    enableFallback: boolean;
    skipLargeImages: boolean;
    maxImageSize: number; // bytes
    outputFormat: 'png' | 'webp' | 'jpg';
    quality: number;
}

export interface DdsProcessingResult {
    success: boolean;
    imageData?: string; // base64 data URL
    width?: number;
    height?: number;
    format?: string;
    processingTime: number;
    memoryUsed: number;
    error?: string;
    usedFallback: boolean;
}

/**
 * Memory-safe DDS processing that avoids S4TK memory issues
 */
export class MemorySafeDdsProcessor {
    private static readonly DEFAULT_OPTIONS: DdsProcessingOptions = {
        maxMemoryUsage: 32 * 1024 * 1024, // 32MB max
        enableFallback: true,
        skipLargeImages: true,
        maxImageSize: 4 * 1024 * 1024, // 4MB max
        outputFormat: 'png',
        quality: 80
    };

    /**
     * Process DDS image with memory safety
     */
    public static async processDdsImage(
        ddsBuffer: Buffer,
        options: Partial<DdsProcessingOptions> = {}
    ): Promise<DdsProcessingResult> {
        const startTime = performance.now();
        const startMemory = process.memoryUsage().heapUsed;
        
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        
        const result: DdsProcessingResult = {
            success: false,
            processingTime: 0,
            memoryUsed: 0,
            usedFallback: false
        };

        try {
            // Check input size
            if (ddsBuffer.length > opts.maxImageSize) {
                if (opts.skipLargeImages) {
                    throw new Error(`Image too large: ${ddsBuffer.length} bytes > ${opts.maxImageSize} bytes`);
                }
            }

            // Check available memory
            const memoryUsage = process.memoryUsage();
            if (memoryUsage.heapUsed > opts.maxMemoryUsage) {
                throw new Error('Insufficient memory for DDS processing');
            }

            // Try S4TK processing with memory monitoring
            try {
                const s4tkResult = await this.tryS4tkProcessing(ddsBuffer, opts);
                if (s4tkResult.success) {
                    result.success = true;
                    result.imageData = s4tkResult.imageData;
                    result.width = s4tkResult.width;
                    result.height = s4tkResult.height;
                    result.format = s4tkResult.format;
                    return result;
                }
            } catch (s4tkError) {
                console.warn('S4TK DDS processing failed, trying fallback:', s4tkError);
                
                // Check if it's the memory error we're trying to fix
                if (s4tkError instanceof Error && s4tkError.message.includes('Cannot enlarge memory arrays')) {
                    console.log('🔧 [MemorySafeDds] Detected S4TK memory limit error, using fallback');
                }
            }

            // Fallback processing
            if (opts.enableFallback) {
                const fallbackResult = await this.fallbackProcessing(ddsBuffer, opts);
                result.success = fallbackResult.success;
                result.imageData = fallbackResult.imageData;
                result.width = fallbackResult.width;
                result.height = fallbackResult.height;
                result.format = fallbackResult.format;
                result.usedFallback = true;
                result.error = fallbackResult.error;
            }

        } catch (error) {
            result.error = error instanceof Error ? error.message : String(error);
        } finally {
            const endTime = performance.now();
            const endMemory = process.memoryUsage().heapUsed;
            
            result.processingTime = endTime - startTime;
            result.memoryUsed = Math.max(0, endMemory - startMemory);
        }

        return result;
    }

    /**
     * Try S4TK processing with memory monitoring
     */
    private static async tryS4tkProcessing(
        ddsBuffer: Buffer,
        options: DdsProcessingOptions
    ): Promise<DdsProcessingResult> {
        return new Promise((resolve, reject) => {
            // Set timeout to prevent hanging
            const timeout = setTimeout(() => {
                reject(new Error('S4TK processing timeout'));
            }, 5000);

            try {
                // Monitor memory before processing
                const beforeMemory = process.memoryUsage().heapUsed;
                
                // Create DDS image
                const ddsImage = DdsImage.from(ddsBuffer);
                
                // Check memory after creation
                const afterCreation = process.memoryUsage().heapUsed;
                if (afterCreation - beforeMemory > options.maxMemoryUsage / 2) {
                    throw new Error('Memory usage too high after DDS creation');
                }

                // Convert to bitmap
                const bitmap = ddsImage.toBitmap();
                
                // Check memory after bitmap conversion
                const afterBitmap = process.memoryUsage().heapUsed;
                if (afterBitmap - beforeMemory > options.maxMemoryUsage) {
                    throw new Error('Memory usage too high after bitmap conversion');
                }

                // Convert to base64
                const base64Data = `data:image/png;base64,${bitmap.toString('base64')}`;
                
                clearTimeout(timeout);
                resolve({
                    success: true,
                    imageData: base64Data,
                    width: ddsImage.width,
                    height: ddsImage.height,
                    format: 'png',
                    processingTime: 0,
                    memoryUsed: 0,
                    usedFallback: false
                });

            } catch (error) {
                clearTimeout(timeout);
                reject(error);
            }
        });
    }

    /**
     * Fallback processing when S4TK fails
     */
    private static async fallbackProcessing(
        ddsBuffer: Buffer,
        options: DdsProcessingOptions
    ): Promise<DdsProcessingResult> {
        try {
            // Basic DDS header parsing
            const header = this.parseDdsHeader(ddsBuffer);
            
            if (!header.isValid) {
                throw new Error('Invalid DDS header');
            }

            // Generate placeholder image with DDS info
            const placeholderSvg = this.generateDdsPlaceholder(
                header.width || 256,
                header.height || 256,
                header.format || 'DDS'
            );

            return {
                success: true,
                imageData: placeholderSvg,
                width: header.width || 256,
                height: header.height || 256,
                format: 'svg',
                processingTime: 0,
                memoryUsed: 0,
                usedFallback: true
            };

        } catch (error) {
            return {
                success: false,
                processingTime: 0,
                memoryUsed: 0,
                usedFallback: true,
                error: error instanceof Error ? error.message : String(error)
            };
        }
    }

    /**
     * Parse DDS header for basic information
     */
    private static parseDdsHeader(buffer: Buffer): {
        isValid: boolean;
        width?: number;
        height?: number;
        format?: string;
    } {
        try {
            // Check DDS signature
            if (buffer.length < 128) return { isValid: false };
            
            const signature = buffer.toString('ascii', 0, 4);
            if (signature !== 'DDS ') return { isValid: false };

            // Read header fields
            const headerSize = buffer.readUInt32LE(4);
            if (headerSize !== 124) return { isValid: false };

            const height = buffer.readUInt32LE(12);
            const width = buffer.readUInt32LE(16);
            
            // Basic format detection
            let format = 'DDS';
            const fourCC = buffer.toString('ascii', 84, 88);
            if (fourCC === 'DXT1') format = 'DXT1';
            else if (fourCC === 'DXT3') format = 'DXT3';
            else if (fourCC === 'DXT5') format = 'DXT5';

            return {
                isValid: true,
                width,
                height,
                format
            };

        } catch (error) {
            return { isValid: false };
        }
    }

    /**
     * Generate placeholder SVG for DDS images
     */
    private static generateDdsPlaceholder(width: number, height: number, format: string): string {
        const svg = `
            <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#e5e7eb;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#f3f4f6;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#grad)"/>
                <rect x="10%" y="10%" width="80%" height="80%" fill="none" stroke="#d1d5db" stroke-width="2" stroke-dasharray="5,5"/>
                <text x="50%" y="45%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">
                    ${format}
                </text>
                <text x="50%" y="60%" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#9ca3af">
                    ${width}×${height}
                </text>
            </svg>
        `;
        
        return `data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`;
    }

    /**
     * Check if DDS processing is likely to succeed
     */
    public static canProcessSafely(ddsBuffer: Buffer, options: Partial<DdsProcessingOptions> = {}): boolean {
        const opts = { ...this.DEFAULT_OPTIONS, ...options };
        
        // Check size
        if (ddsBuffer.length > opts.maxImageSize) return false;
        
        // Check available memory
        const memoryUsage = process.memoryUsage();
        if (memoryUsage.heapUsed > opts.maxMemoryUsage * 0.8) return false;
        
        // Check DDS header
        const header = this.parseDdsHeader(ddsBuffer);
        if (!header.isValid) return false;
        
        // Check dimensions
        if (header.width && header.height) {
            const estimatedSize = header.width * header.height * 4; // RGBA
            if (estimatedSize > opts.maxMemoryUsage / 4) return false;
        }
        
        return true;
    }

    /**
     * Get memory usage statistics
     */
    public static getMemoryStats(): {
        heapUsed: number;
        heapTotal: number;
        external: number;
        rss: number;
    } {
        return process.memoryUsage();
    }

    /**
     * Force garbage collection if available
     */
    public static forceGarbageCollection(): void {
        if (global.gc) {
            global.gc();
        }
    }
}
