/**
 * Performance Testing Tool for Simonitor
 * Tests the optimized analysis system with different collection sizes
 */

import * as fs from 'fs/promises';
import * as path from 'path';
import { PackageAnalysisService } from '../services/analysis/PackageAnalysisService';
import { AnalysisCacheService } from '../services/cache/AnalysisCacheService';

interface PerformanceTestResult {
    testName: string;
    fileCount: number;
    totalTime: number;
    averageTimePerFile: number;
    cacheHitRate: number;
    memoryUsage: NodeJS.MemoryUsage;
    errors: number;
}

class PerformanceTester {
    private analysisService: PackageAnalysisService;
    private cacheService: AnalysisCacheService;

    constructor() {
        this.analysisService = new PackageAnalysisService();
        this.cacheService = new AnalysisCacheService({
            maxEntries: 10000,
            maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
            enablePersistence: false, // Disable for testing
            cacheDirectory: path.join(process.cwd(), 'temp-cache')
        });
    }

    async initialize(): Promise<void> {
        await this.cacheService.initialize();
        console.log('🚀 [PerformanceTest] Initialized');
    }

    async collectModFiles(modsPath: string, maxFiles?: number): Promise<string[]> {
        const files: string[] = [];
        
        async function scanDirectory(dirPath: string): Promise<void> {
            try {
                const items = await fs.readdir(dirPath, { withFileTypes: true });
                for (const item of items) {
                    if (maxFiles && files.length >= maxFiles) break;
                    
                    const fullPath = path.join(dirPath, item.name);
                    if (item.isDirectory()) {
                        await scanDirectory(fullPath);
                    } else if (item.isFile()) {
                        const ext = path.extname(item.name).toLowerCase();
                        if (ext === '.package' || ext === '.ts4script') {
                            files.push(fullPath);
                        }
                    }
                }
            } catch (error) {
                console.warn(`Failed to scan directory ${dirPath}:`, error);
            }
        }

        await scanDirectory(modsPath);
        return files.slice(0, maxFiles);
    }

    async testAnalysisPerformance(
        files: string[], 
        testName: string,
        useCache: boolean = true
    ): Promise<PerformanceTestResult> {
        console.log(`\n🔬 [PerformanceTest] Starting ${testName} (${files.length} files, cache: ${useCache})`);
        
        const startTime = Date.now();
        const startMemory = process.memoryUsage();
        let cacheHits = 0;
        let errors = 0;

        for (let i = 0; i < files.length; i++) {
            const filePath = files[i];
            const fileName = path.basename(filePath);
            
            try {
                let result = null;
                let cacheHit = false;

                // Check cache first if enabled
                if (useCache) {
                    result = await this.cacheService.get(filePath);
                    if (result) {
                        cacheHit = true;
                        cacheHits++;
                    }
                }

                // Analyze if not cached
                if (!result) {
                    const buffer = await fs.readFile(filePath);
                    result = await this.analysisService.detailedAnalyzeAsync(buffer, filePath);
                    
                    // Cache the result if caching is enabled
                    if (useCache) {
                        await this.cacheService.set(filePath, result);
                    }
                }

                // Progress reporting
                if ((i + 1) % 10 === 0 || i === files.length - 1) {
                    const progress = ((i + 1) / files.length) * 100;
                    const elapsed = Date.now() - startTime;
                    const avgTime = elapsed / (i + 1);
                    console.log(`📊 [PerformanceTest] Progress: ${progress.toFixed(1)}% (${i + 1}/${files.length}) - Avg: ${avgTime.toFixed(2)}ms/file - Cache hits: ${cacheHits}`);
                }

            } catch (error) {
                errors++;
                console.error(`❌ [PerformanceTest] Failed to analyze ${fileName}:`, error);
            }
        }

        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const totalTime = endTime - startTime;
        const averageTimePerFile = totalTime / files.length;
        const cacheHitRate = files.length > 0 ? (cacheHits / files.length) * 100 : 0;

        const result: PerformanceTestResult = {
            testName,
            fileCount: files.length,
            totalTime,
            averageTimePerFile,
            cacheHitRate,
            memoryUsage: {
                rss: endMemory.rss - startMemory.rss,
                heapUsed: endMemory.heapUsed - startMemory.heapUsed,
                heapTotal: endMemory.heapTotal - startMemory.heapTotal,
                external: endMemory.external - startMemory.external,
                arrayBuffers: endMemory.arrayBuffers - startMemory.arrayBuffers
            },
            errors
        };

        console.log(`✅ [PerformanceTest] ${testName} completed:`);
        console.log(`   📁 Files: ${result.fileCount}`);
        console.log(`   ⏱️  Total time: ${(result.totalTime / 1000).toFixed(2)}s`);
        console.log(`   📈 Avg per file: ${result.averageTimePerFile.toFixed(2)}ms`);
        console.log(`   💾 Cache hit rate: ${result.cacheHitRate.toFixed(1)}%`);
        console.log(`   🧠 Memory delta: ${(result.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
        console.log(`   ❌ Errors: ${result.errors}`);

        return result;
    }

    async runComprehensiveTest(modsPath: string): Promise<PerformanceTestResult[]> {
        const results: PerformanceTestResult[] = [];

        // Test 1: Small collection (10 files) - No cache
        console.log('\n🎯 [PerformanceTest] Test 1: Small collection baseline (no cache)');
        const smallFiles = await this.collectModFiles(modsPath, 10);
        await this.cacheService.clear(); // Clear cache
        const test1 = await this.testAnalysisPerformance(smallFiles, 'Small Collection (No Cache)', false);
        results.push(test1);

        // Test 2: Small collection (10 files) - With cache (first run)
        console.log('\n🎯 [PerformanceTest] Test 2: Small collection with cache (first run)');
        await this.cacheService.clear(); // Clear cache
        const test2 = await this.testAnalysisPerformance(smallFiles, 'Small Collection (Cache - First Run)', true);
        results.push(test2);

        // Test 3: Small collection (10 files) - With cache (second run - should have cache hits)
        console.log('\n🎯 [PerformanceTest] Test 3: Small collection with cache (second run)');
        const test3 = await this.testAnalysisPerformance(smallFiles, 'Small Collection (Cache - Second Run)', true);
        results.push(test3);

        // Test 4: Medium collection (50 files) - With cache
        console.log('\n🎯 [PerformanceTest] Test 4: Medium collection with cache');
        const mediumFiles = await this.collectModFiles(modsPath, 50);
        await this.cacheService.clear(); // Clear cache
        const test4 = await this.testAnalysisPerformance(mediumFiles, 'Medium Collection (50 files)', true);
        results.push(test4);

        // Test 5: Large collection (200 files) - With cache
        console.log('\n🎯 [PerformanceTest] Test 5: Large collection with cache');
        const largeFiles = await this.collectModFiles(modsPath, 200);
        await this.cacheService.clear(); // Clear cache
        const test5 = await this.testAnalysisPerformance(largeFiles, 'Large Collection (200 files)', true);
        results.push(test5);

        return results;
    }

    printSummary(results: PerformanceTestResult[]): void {
        console.log('\n📊 [PerformanceTest] PERFORMANCE SUMMARY');
        console.log('='.repeat(80));
        
        results.forEach((result, index) => {
            console.log(`${index + 1}. ${result.testName}`);
            console.log(`   Files: ${result.fileCount} | Time: ${(result.totalTime / 1000).toFixed(2)}s | Avg: ${result.averageTimePerFile.toFixed(2)}ms | Cache: ${result.cacheHitRate.toFixed(1)}%`);
        });

        console.log('\n🎯 [PerformanceTest] KEY METRICS:');
        const cacheTest = results.find(r => r.testName.includes('Second Run'));
        if (cacheTest) {
            console.log(`   💾 Cache effectiveness: ${cacheTest.cacheHitRate.toFixed(1)}% hit rate`);
            console.log(`   ⚡ Cache speedup: ${cacheTest.averageTimePerFile.toFixed(2)}ms per file`);
        }

        const largestTest = results.reduce((prev, current) => 
            current.fileCount > prev.fileCount ? current : prev
        );
        console.log(`   📈 Largest test: ${largestTest.fileCount} files in ${(largestTest.totalTime / 1000).toFixed(2)}s`);
        console.log(`   🚀 Projected 10K files: ~${((largestTest.averageTimePerFile * 10000) / 1000 / 60).toFixed(1)} minutes`);
    }

    async cleanup(): Promise<void> {
        await this.cacheService.shutdown();
        console.log('🧹 [PerformanceTest] Cleanup complete');
    }
}

// Main execution
async function main() {
    const modsPath = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';
    
    const tester = new PerformanceTester();
    
    try {
        await tester.initialize();
        const results = await tester.runComprehensiveTest(modsPath);
        tester.printSummary(results);
    } catch (error) {
        console.error('❌ [PerformanceTest] Test failed:', error);
    } finally {
        await tester.cleanup();
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(console.error);
}
