/**
 * Vue 3 Composable: Auto-Scan Management
 * Manages automatic mod collection scanning on application startup
 * Follows Vue 3 Composition API best practices with performance optimization
 */

import { ref, computed, watch, onMounted, onUnmounted, type Ref } from 'vue';
import type { AnalyzedPackage } from '../../types/analysis';
import { ScanCacheService } from '../services/ScanCacheService';

// Auto-scan state interface
interface AutoScanState {
  isAutoScanning: boolean;
  scanProgress: number;
  totalFiles: number;
  processedFiles: number;
  currentFile: string;
  estimatedTimeRemaining: number;
  canCancel: boolean;
  scanResults: AnalyzedPackage[];
  scanError: string | null;
  lastScanTime: Date | null;
  cacheHitRate: number;
}

// Auto-scan options interface
interface AutoScanOptions {
  enableCaching: boolean;
  allowBackgroundScanning: boolean;
  maxConcurrentFiles: number;
  progressUpdateInterval: number;
  enableProgressiveLoading: boolean;
}

// Default auto-scan options
const defaultOptions: AutoScanOptions = {
  enableCaching: true,
  allowBackgroundScanning: true,
  maxConcurrentFiles: 3,
  progressUpdateInterval: 100, // ms
  enableProgressiveLoading: true,
};

// Utility functions
const getDefaultModsFolder = async (): Promise<string> => {
  try {
    // Try to get the default folder from Electron API
    if (window.electronAPI?.getDefaultModsFolder) {
      const result = await window.electronAPI.getDefaultModsFolder();
      if (result.success && result.path) {
        return result.path;
      }
    }
  } catch (error) {
    console.warn('[useAutoScan] Failed to get default mods folder from API:', error);
  }

  // Fallback - this shouldn't be reached if Electron API is working
  throw new Error('Could not detect default mods folder');
};

const getStoredModsFolder = (): string => {
  try {
    // Ensure settings have auto-scan defaults first
    ensureSettingsHaveAutoScanDefaults();

    const settings = localStorage.getItem('simonitor-settings');
    if (settings) {
      const parsedSettings = JSON.parse(settings);
      return parsedSettings.defaultModsFolder || '';
    }
  } catch (error) {
    console.warn('[useAutoScan] Failed to get stored mods folder:', error);
  }
  return '';
};

const storeModsFolder = (folderPath: string): void => {
  try {
    const settings = localStorage.getItem('simonitor-settings');
    const parsedSettings = settings ? JSON.parse(settings) : {};
    parsedSettings.defaultModsFolder = folderPath;
    localStorage.setItem('simonitor-settings', JSON.stringify(parsedSettings));
  } catch (error) {
    console.error('[useAutoScan] Failed to store mods folder:', error);
  }
};

const isAutoScanEnabled = (): boolean => {
  try {
    // First ensure settings have auto-scan defaults
    ensureSettingsHaveAutoScanDefaults();

    const settings = localStorage.getItem('simonitor-settings');
    console.log('🔍 [useAutoScan] Raw settings from localStorage:', settings);

    if (settings) {
      const parsedSettings = JSON.parse(settings);
      console.log('🔍 [useAutoScan] Parsed settings:', parsedSettings);
      console.log('🔍 [useAutoScan] enableAutoScan value:', parsedSettings.enableAutoScan);
      return parsedSettings.enableAutoScan === true;
    } else {
      console.log('🔍 [useAutoScan] No settings found in localStorage, defaulting to auto-scan ENABLED');
      return true; // Default to enabled when no settings exist
    }
  } catch (error) {
    console.warn('[useAutoScan] Failed to check auto-scan setting:', error);
    return true; // Default to enabled on error
  }
};

const formatTimeRemaining = (seconds: number): string => {
  if (seconds < 60) return `${Math.round(seconds)}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.round(seconds % 60);
  return `${minutes}m ${remainingSeconds}s`;
};

const ensureSettingsHaveAutoScanDefaults = (): void => {
  try {
    const settings = localStorage.getItem('simonitor-settings');

    if (settings) {
      const parsedSettings = JSON.parse(settings);

      // Check if auto-scan properties are missing
      if (parsedSettings.enableAutoScan === undefined) {
        console.log('🔧 [useAutoScan] Auto-scan properties missing, adding defaults...');

        // Add auto-scan defaults
        parsedSettings.enableAutoScan = true;
        parsedSettings.defaultModsFolder = '';
        parsedSettings.enableScanCaching = true;
        parsedSettings.allowBackgroundScanning = true;

        // Save updated settings
        localStorage.setItem('simonitor-settings', JSON.stringify(parsedSettings));
        console.log('✅ [useAutoScan] Settings updated with auto-scan defaults');
      }
    } else {
      // No settings exist, create with auto-scan enabled
      const defaultSettings = {
        enableAutoScan: true,
        defaultModsFolder: '',
        enableScanCaching: true,
        allowBackgroundScanning: true,
        autoExpandFirstFile: true,
        showResourceSizes: true,
        enableOverrideDetection: false,
        resultsPerPage: '50',
        defaultSortField: 'key',
        useMonospaceFont: true,
        defaultExportFormat: 'json',
        includeFilePathInExport: true,
        analysisTimeout: 30,
        enableDebugMode: false,
        enableEnhancedCASDetection: true,
        showHairClassificationDetails: true,
        hairConfidenceThreshold: 0.5,
      };

      localStorage.setItem('simonitor-settings', JSON.stringify(defaultSettings));
      console.log('✅ [useAutoScan] Created default settings with auto-scan enabled');
    }
  } catch (error) {
    console.warn('[useAutoScan] Failed to ensure settings defaults:', error);
  }
};

export function useAutoScan(options: Partial<AutoScanOptions> = {}) {
  // Merge options with defaults
  const opts = { ...defaultOptions, ...options };

  // Reactive state
  const isAutoScanning = ref(false);
  const scanProgress = ref(0);
  const totalFiles = ref(0);
  const processedFiles = ref(0);
  const currentFile = ref('');
  const estimatedTimeRemaining = ref(0);
  const canCancel = ref(true);
  const scanResults = ref<AnalyzedPackage[]>([]);
  const scanError = ref<string | null>(null);
  const lastScanTime = ref<Date | null>(null);
  const cacheHitRate = ref(0);

  // Internal state for performance tracking
  const scanStartTime = ref<number>(0);
  const filesPerSecond = ref(0);
  const abortController = ref<AbortController | null>(null);

  // Computed properties
  const progressPercentage = computed(() => {
    if (totalFiles.value === 0) return 0;
    return Math.round((processedFiles.value / totalFiles.value) * 100);
  });

  const scanStatus = computed(() => {
    if (scanError.value) return 'error';
    if (isAutoScanning.value) return 'scanning';
    if (scanResults.value.length > 0) return 'complete';
    return 'idle';
  });

  const formattedTimeRemaining = computed(() => {
    return formatTimeRemaining(estimatedTimeRemaining.value);
  });

  const scanSummary = computed(() => {
    return {
      totalFiles: totalFiles.value,
      processedFiles: processedFiles.value,
      successfulFiles: scanResults.value.length,
      failedFiles: processedFiles.value - scanResults.value.length,
      cacheHitRate: cacheHitRate.value,
      processingSpeed: filesPerSecond.value,
      duration: lastScanTime.value ? Date.now() - scanStartTime.value : 0,
    };
  });

  // Watch for progress changes to update time estimation
  watch([processedFiles, totalFiles], ([processed, total]) => {
    if (processed > 0 && total > 0 && isAutoScanning.value) {
      const elapsed = (Date.now() - scanStartTime.value) / 1000;
      filesPerSecond.value = processed / elapsed;
      const remaining = total - processed;
      estimatedTimeRemaining.value = remaining / filesPerSecond.value;
    }
  });

  // Methods
  const startAutoScan = async (folderPath?: string): Promise<void> => {
    if (isAutoScanning.value) {
      console.warn('[useAutoScan] Scan already in progress');
      return;
    }

    try {
      // Reset state
      resetScanState();
      isAutoScanning.value = true;
      scanStartTime.value = Date.now();
      abortController.value = new AbortController();

      // Determine target folder
      let targetFolder = folderPath;

      if (!targetFolder) {
        // Try stored folder first
        targetFolder = getStoredModsFolder();

        // If no stored folder, get default
        if (!targetFolder) {
          targetFolder = await getDefaultModsFolder();
          // Store the default for future use
          storeModsFolder(targetFolder);
        }
      }

      console.log(`🚀 [useAutoScan] Starting automatic scan of: ${targetFolder}`);

      // Check if Electron API is available
      if (!window.electronAPI) {
        throw new Error('Electron API not available');
      }

      // Set up progress listener for background scanning
      const progressHandler = (progress: any) => {
        totalFiles.value = progress.totalFiles || 0;
        processedFiles.value = progress.processedFiles || 0;
        currentFile.value = progress.currentFile || '';
        estimatedTimeRemaining.value = progress.estimatedTimeRemaining || 0;

        if (progress.type === 'file-complete') {
          scanProgress.value = progress.progress || 0;
        }
      };

      // Use background scanning if available and enabled
      const useBackground = opts.allowBackgroundScanning && window.electronAPI.analyzeModsFolderBackground;

      if (useBackground) {
        window.electronAPI.onScanProgress(progressHandler);
      }

      // Start the folder analysis
      const result = useBackground
        ? await window.electronAPI.analyzeModsFolderBackground(targetFolder, {
            enableCaching: opts.enableCaching,
            maxConcurrent: opts.maxConcurrentFiles
          })
        : await window.electronAPI.analyzeModsFolder(targetFolder);

      // Clean up progress listener
      if (useBackground) {
        window.electronAPI.offScanProgress(progressHandler);
      }

      if (abortController.value?.signal.aborted) {
        console.log('🛑 [useAutoScan] Scan was cancelled');
        return;
      }

      if (result.success && result.data) {
        scanResults.value = result.data;
        lastScanTime.value = new Date();

        // Store the successfully scanned folder
        storeModsFolder(targetFolder);

        console.log(`✅ [useAutoScan] Scan completed: ${result.data.length} mods analyzed`);
      } else {
        throw new Error(result.error || 'Failed to analyze mods folder');
      }

    } catch (error) {
      console.error('❌ [useAutoScan] Scan failed:', error);
      scanError.value = error instanceof Error ? error.message : 'Unknown scan error';
    } finally {
      isAutoScanning.value = false;
      abortController.value = null;
    }
  };

  const cancelScan = (): void => {
    if (abortController.value && canCancel.value) {
      console.log('🛑 [useAutoScan] Cancelling scan...');
      abortController.value.abort();
      isAutoScanning.value = false;
      scanError.value = 'Scan cancelled by user';
    }
  };

  const resetScanState = (): void => {
    scanProgress.value = 0;
    totalFiles.value = 0;
    processedFiles.value = 0;
    currentFile.value = '';
    estimatedTimeRemaining.value = 0;
    scanError.value = null;
    cacheHitRate.value = 0;
    filesPerSecond.value = 0;
  };

  const clearResults = (): void => {
    scanResults.value = [];
    lastScanTime.value = null;
    resetScanState();
  };

  const retryLastScan = async (): Promise<void> => {
    if (lastScanTime.value) {
      await startAutoScan();
    }
  };

  const getCacheStats = () => {
    return ScanCacheService.getCacheStats();
  };

  const clearCache = (): void => {
    ScanCacheService.clearCache();
    console.log('🗑️ [useAutoScan] Cache cleared');
  };

  const performCacheMaintenance = async (): Promise<void> => {
    await ScanCacheService.performMaintenance();
    console.log('🧹 [useAutoScan] Cache maintenance completed');
  };

  // Auto-scan on mount if enabled
  onMounted(async () => {
    console.log('🚀 [useAutoScan] Composable mounted, checking auto-scan settings...');

    // Small delay to ensure settings are loaded
    await new Promise(resolve => setTimeout(resolve, 100));

    // Check if auto-scan is enabled in settings
    const autoScanEnabled = isAutoScanEnabled();
    console.log('🔍 [useAutoScan] Auto-scan enabled:', autoScanEnabled);

    if (autoScanEnabled) {
      try {
        console.log('🔄 [useAutoScan] Auto-scan enabled, starting scan...');

        // Get the folder to scan (stored preference or default)
        let folderToScan = getStoredModsFolder();

        if (!folderToScan || folderToScan.trim() === '') {
          // No stored folder or empty folder, try to get and verify default
          console.log('🔍 [useAutoScan] No stored folder, detecting default...');

          if (window.electronAPI?.getDefaultModsFolder) {
            const folderCheck = await window.electronAPI.getDefaultModsFolder();
            if (folderCheck.success && folderCheck.path) {
              folderToScan = folderCheck.path;
              console.log('🔍 [useAutoScan] Default folder detected:', folderToScan);

              // Store the detected folder for future use
              storeModsFolder(folderToScan);

              if (!folderCheck.exists) {
                console.warn('[useAutoScan] Default mods folder does not exist:', folderToScan);
                scanError.value = `Default mods folder not found: ${folderToScan}. Please create the folder or set a custom folder in settings.`;
                return;
              }
            } else {
              console.error('[useAutoScan] Failed to detect default mods folder');
              scanError.value = 'Could not detect default mods folder. Please set a custom folder in settings.';
              return;
            }
          } else {
            console.error('[useAutoScan] Electron API not available for folder detection');
            scanError.value = 'Folder detection not available. Please set a custom folder in settings.';
            return;
          }
        }

        await startAutoScan(folderToScan);
      } catch (error) {
        console.warn('[useAutoScan] Failed to start auto-scan:', error);
        scanError.value = error instanceof Error ? error.message : 'Failed to start automatic scan';
      }
    } else {
      console.log('🔄 [useAutoScan] Auto-scan disabled in settings');
    }
  });

  // Cleanup on unmount
  onUnmounted(() => {
    if (abortController.value) {
      abortController.value.abort();
    }
  });

  return {
    // State
    isAutoScanning,
    scanProgress,
    totalFiles,
    processedFiles,
    currentFile,
    estimatedTimeRemaining,
    canCancel,
    scanResults,
    scanError,
    lastScanTime,
    cacheHitRate,

    // Computed
    progressPercentage,
    scanStatus,
    formattedTimeRemaining,
    scanSummary,

    // Methods
    startAutoScan,
    cancelScan,
    resetScanState,
    clearResults,
    retryLastScan,

    // Cache methods
    getCacheStats,
    clearCache,
    performCacheMaintenance,
  };
}
