/**
 * Streaming Package Parser
 * Optimized S4TK package parsing with streaming support for large files
 * Reduces memory usage and improves performance through incremental processing
 */

import { Package } from '@s4tk/models';
import type { ResourceEntry } from '@s4tk/models/types';
import type { CancellationToken } from '../../../types/analysis-results';

export interface StreamingParseOptions {
    enableResourceSummary: boolean;
    enableEarlyTermination: boolean;
    maxResourcesPreview: number;
    chunkSize: number;
    enableMemoryOptimization: boolean;
}

export interface StreamingParseResult {
    package: Package;
    resourceCount: number;
    resourceSummary: {
        resourceTypes: number[];
        totalSize: number;
        largestResource: number;
        compressionRatio: number;
        hasLargeResources: boolean;
    };
    streamingMetrics: {
        chunksProcessed: number;
        memoryPeakUsage: number;
        processingTime: number;
        earlyTerminated: boolean;
    };
}

/**
 * High-performance streaming parser for S4TK packages
 */
export class StreamingPackageParser {
    private static readonly DEFAULT_CHUNK_SIZE = 64 * 1024; // 64KB chunks
    private static readonly MEMORY_THRESHOLD = 128 * 1024 * 1024; // 128MB threshold

    /**
     * Parse package with streaming optimization
     */
    public async parsePackageStreaming(
        buffer: Buffer,
        fileName: string,
        options: Partial<StreamingParseOptions> = {},
        cancellationToken?: CancellationToken
    ): Promise<StreamingParseResult> {
        const startTime = performance.now();
        const startMemory = process.memoryUsage().heapUsed;

        const opts: StreamingParseOptions = {
            enableResourceSummary: true,
            enableEarlyTermination: true,
            maxResourcesPreview: 100,
            chunkSize: this.DEFAULT_CHUNK_SIZE,
            enableMemoryOptimization: true,
            ...options
        };

        const metrics = {
            chunksProcessed: 0,
            memoryPeakUsage: 0,
            processingTime: 0,
            earlyTerminated: false
        };

        try {
            // Quick validation - be more lenient
            if (!this.validatePackageHeader(buffer)) {
                // Don't throw error, just use standard parsing
                console.log(`Header validation failed for ${fileName}, using standard parsing`);
                return await this.standardParsing(buffer, fileName, opts, metrics);
            }

            // Check if streaming is beneficial
            if (buffer.length < 1024 * 1024 || !opts.enableMemoryOptimization) {
                // Use standard parsing for small files
                return await this.standardParsing(buffer, fileName, opts, metrics);
            }

            // Streaming parsing for large files
            return await this.performStreamingParsing(buffer, fileName, opts, metrics, cancellationToken);

        } catch (error) {
            // Fallback to standard parsing on streaming failure
            console.warn(`Streaming parsing failed for ${fileName}, falling back to standard:`, error);
            return await this.standardParsing(buffer, fileName, opts, metrics);
        } finally {
            metrics.processingTime = performance.now() - startTime;
            metrics.memoryPeakUsage = Math.max(0, process.memoryUsage().heapUsed - startMemory);
        }
    }

    /**
     * Validate DBPF package header
     */
    private validatePackageHeader(buffer: Buffer): boolean {
        if (buffer.length < 96) return false;

        // Check DBPF signature
        const signature = buffer.toString('ascii', 0, 4);
        if (signature !== 'DBPF') return false;

        // More lenient header validation - some packages have variations
        try {
            const headerSize = buffer.readUInt32LE(8);
            // Allow more flexible header sizes
            if (headerSize < 64 || headerSize > 256) return false;
        } catch (error) {
            // If we can't read the header size, but signature is valid, continue
            console.warn('Header size validation failed, but DBPF signature is valid');
        }

        return true;
    }

    /**
     * Standard parsing for small files or fallback
     */
    private async standardParsing(
        buffer: Buffer,
        fileName: string,
        options: StreamingParseOptions,
        metrics: any
    ): Promise<StreamingParseResult> {
        const s4tkPackage = Package.from(buffer, {
            decompressBuffers: false,
            loadRaw: true,
            limit: options.maxResourcesPreview
        });

        const resourceSummary = options.enableResourceSummary 
            ? this.generateResourceSummary(s4tkPackage, buffer.length)
            : this.getEmptyResourceSummary();

        return {
            package: s4tkPackage,
            resourceCount: s4tkPackage.size,
            resourceSummary,
            streamingMetrics: metrics
        };
    }

    /**
     * Perform streaming parsing for large files
     */
    private async performStreamingParsing(
        buffer: Buffer,
        fileName: string,
        options: StreamingParseOptions,
        metrics: any,
        cancellationToken?: CancellationToken
    ): Promise<StreamingParseResult> {
        // Parse DBPF header first
        const header = this.parseDBPFHeader(buffer);
        
        // Create package with selective loading
        const s4tkPackage = Package.from(buffer, {
            decompressBuffers: false,
            loadRaw: true,
            resourceFilter: this.createStreamingResourceFilter(options),
            limit: options.maxResourcesPreview
        });

        // Generate resource summary with streaming
        const resourceSummary = await this.generateStreamingResourceSummary(
            s4tkPackage,
            buffer,
            header,
            options,
            metrics,
            cancellationToken
        );

        return {
            package: s4tkPackage,
            resourceCount: header.resourceCount || s4tkPackage.size,
            resourceSummary,
            streamingMetrics: metrics
        };
    }

    /**
     * Parse DBPF header information
     */
    private parseDBPFHeader(buffer: Buffer): {
        version: number;
        resourceCount: number;
        indexOffset: number;
        indexSize: number;
    } {
        return {
            version: buffer.readUInt32LE(4),
            resourceCount: buffer.readUInt32LE(36),
            indexOffset: buffer.readUInt32LE(40),
            indexSize: buffer.readUInt32LE(44)
        };
    }

    /**
     * Create resource filter for streaming
     */
    private createStreamingResourceFilter(options: StreamingParseOptions): ((type: number) => boolean) | undefined {
        if (!options.enableEarlyTermination) return undefined;

        // Priority resource types for quick categorization
        const priorityTypes = new Set([
            0x034AEECB, // CAS Part
            0x0354796A, // CAS Modifier
            0x00AE6C67, // CAS Preset
            0x3C1AF1F2, // CAS Thumbnail
            0x319E4F1D, // Object Definition
            0x0C772E27, // Object Catalog
            0x00B2D882, // DST Image
            0x62E94D38, // Tuning
            0x220557DA, // SimData
            0x2E75C764  // PNG Image
        ]);

        return (type: number) => priorityTypes.has(type);
    }

    /**
     * Generate resource summary with streaming
     */
    private async generateStreamingResourceSummary(
        s4tkPackage: Package,
        buffer: Buffer,
        header: any,
        options: StreamingParseOptions,
        metrics: any,
        cancellationToken?: CancellationToken
    ): Promise<any> {
        const resourceTypes = new Set<number>();
        let totalSize = 0;
        let largestResource = 0;
        let processedCount = 0;

        // Process resources in chunks to avoid memory pressure
        for (const [key, entry] of s4tkPackage.entries()) {
            if (cancellationToken?.isCancelled) {
                metrics.earlyTerminated = true;
                break;
            }

            resourceTypes.add(key.type);
            const resourceSize = entry.value?.byteLength || 0;
            totalSize += resourceSize;
            largestResource = Math.max(largestResource, resourceSize);
            processedCount++;

            // Check memory pressure
            if (options.enableMemoryOptimization && processedCount % 50 === 0) {
                const currentMemory = process.memoryUsage().heapUsed;
                if (currentMemory > this.MEMORY_THRESHOLD) {
                    console.log(`Memory threshold reached, stopping resource enumeration at ${processedCount} resources`);
                    metrics.earlyTerminated = true;
                    break;
                }
            }

            // Early termination for quick analysis
            if (options.enableEarlyTermination && processedCount >= options.maxResourcesPreview) {
                metrics.earlyTerminated = true;
                break;
            }

            // Yield control periodically
            if (processedCount % 25 === 0) {
                await new Promise(resolve => setImmediate(resolve));
                metrics.chunksProcessed++;
            }
        }

        const compressionRatio = buffer.length > 0 ? totalSize / buffer.length : 0;

        return {
            resourceTypes: Array.from(resourceTypes),
            totalSize,
            largestResource,
            compressionRatio,
            hasLargeResources: largestResource > 1024 * 1024 // 1MB+
        };
    }

    /**
     * Generate resource summary from package
     */
    private generateResourceSummary(s4tkPackage: Package, bufferSize: number): any {
        const resourceTypes = new Set<number>();
        let totalSize = 0;
        let largestResource = 0;

        // Quick enumeration
        for (const [key, entry] of s4tkPackage.entries()) {
            resourceTypes.add(key.type);
            const resourceSize = entry.value?.byteLength || 0;
            totalSize += resourceSize;
            largestResource = Math.max(largestResource, resourceSize);
        }

        const compressionRatio = bufferSize > 0 ? totalSize / bufferSize : 0;

        return {
            resourceTypes: Array.from(resourceTypes),
            totalSize,
            largestResource,
            compressionRatio,
            hasLargeResources: largestResource > 1024 * 1024
        };
    }

    /**
     * Get empty resource summary
     */
    private getEmptyResourceSummary(): any {
        return {
            resourceTypes: [],
            totalSize: 0,
            largestResource: 0,
            compressionRatio: 0,
            hasLargeResources: false
        };
    }

    /**
     * Check if streaming would be beneficial
     */
    public static shouldUseStreaming(buffer: Buffer, options: Partial<StreamingParseOptions> = {}): boolean {
        // Use streaming for files larger than 1MB
        if (buffer.length > 1024 * 1024) return true;
        
        // Use streaming if memory optimization is enabled
        if (options.enableMemoryOptimization) return true;
        
        // Check current memory usage
        const memoryUsage = process.memoryUsage();
        if (memoryUsage.heapUsed > 256 * 1024 * 1024) return true; // 256MB+
        
        return false;
    }

    /**
     * Get optimal chunk size based on file size
     */
    public static getOptimalChunkSize(bufferSize: number): number {
        if (bufferSize < 1024 * 1024) return 32 * 1024; // 32KB for small files
        if (bufferSize < 10 * 1024 * 1024) return 64 * 1024; // 64KB for medium files
        return 128 * 1024; // 128KB for large files
    }
}
