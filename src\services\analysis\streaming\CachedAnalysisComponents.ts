/**
 * Cached Analysis Components
 * Aggressive caching system for analysis results and intermediate data
 */

import * as crypto from 'crypto';
import type { OptimizedAnalysisResult } from '../OptimizedAnalysisEngine';

export interface CacheOptions {
    enablePersistentCache: boolean;
    enableMemoryCache: boolean;
    maxMemoryCacheSize: number;
    maxAge: number;
    enableCompression: boolean;
}

export interface CacheStats {
    memoryHits: number;
    memoryMisses: number;
    memorySize: number;
    hitRate: number;
    averageRetrievalTime: number;
}

/**
 * High-performance caching for analysis components
 */
export class CachedAnalysisComponents {
    private static memoryCache = new Map<string, any>();
    private static cacheStats: CacheStats = {
        memoryHits: 0,
        memoryMisses: 0,
        memorySize: 0,
        hitRate: 0,
        averageRetrievalTime: 0
    };

    private readonly options: CacheOptions;

    constructor(options: Partial<CacheOptions> = {}) {
        this.options = {
            enablePersistentCache: false, // Disabled for performance
            enableMemoryCache: true,
            maxMemoryCacheSize: 10000,
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            enableCompression: false, // Disabled for speed
            ...options
        };
    }

    /**
     * Get cached analysis result
     */
    public async getCachedResult(filePath: string, buffer: Buffer): Promise<OptimizedAnalysisResult | null> {
        const startTime = performance.now();
        
        try {
            const cacheKey = this.generateCacheKey(filePath, buffer);
            
            // Check memory cache
            if (this.options.enableMemoryCache) {
                const memoryResult = this.getFromMemoryCache(cacheKey);
                if (memoryResult) {
                    CachedAnalysisComponents.cacheStats.memoryHits++;
                    CachedAnalysisComponents.cacheStats.averageRetrievalTime = 
                        (CachedAnalysisComponents.cacheStats.averageRetrievalTime + (performance.now() - startTime)) / 2;
                    return memoryResult;
                }
            }

            CachedAnalysisComponents.cacheStats.memoryMisses++;
            return null;

        } catch (error) {
            console.warn('Cache retrieval error:', error);
            return null;
        }
    }

    /**
     * Cache analysis result
     */
    public async cacheResult(filePath: string, buffer: Buffer, result: OptimizedAnalysisResult): Promise<void> {
        try {
            const cacheKey = this.generateCacheKey(filePath, buffer);
            
            // Cache in memory
            if (this.options.enableMemoryCache) {
                this.setInMemoryCache(cacheKey, result);
            }

        } catch (error) {
            console.warn('Cache storage error:', error);
        }
    }

    /**
     * Get from memory cache
     */
    private getFromMemoryCache(cacheKey: string): OptimizedAnalysisResult | null {
        const cached = CachedAnalysisComponents.memoryCache.get(cacheKey);
        
        if (cached && (Date.now() - cached.timestamp) < this.options.maxAge) {
            return cached.data;
        }
        
        if (cached) {
            // Remove expired entry
            CachedAnalysisComponents.memoryCache.delete(cacheKey);
            CachedAnalysisComponents.cacheStats.memorySize--;
        }
        
        return null;
    }

    /**
     * Set in memory cache
     */
    private setInMemoryCache(cacheKey: string, result: OptimizedAnalysisResult): void {
        // Check cache size limit
        if (CachedAnalysisComponents.memoryCache.size >= this.options.maxMemoryCacheSize) {
            this.evictOldestEntries();
        }

        CachedAnalysisComponents.memoryCache.set(cacheKey, {
            data: result,
            timestamp: Date.now()
        });

        CachedAnalysisComponents.cacheStats.memorySize = CachedAnalysisComponents.memoryCache.size;
    }

    /**
     * Evict oldest cache entries
     */
    private evictOldestEntries(): void {
        const entries = Array.from(CachedAnalysisComponents.memoryCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
        
        // Remove oldest 20%
        const toRemove = Math.floor(entries.length * 0.2);
        for (let i = 0; i < toRemove; i++) {
            CachedAnalysisComponents.memoryCache.delete(entries[i][0]);
        }
    }

    /**
     * Generate cache key
     */
    private generateCacheKey(filePath: string, buffer: Buffer): string {
        // Use file path and size for quick key generation
        const pathHash = crypto.createHash('md5').update(filePath).digest('hex').substring(0, 8);
        return `${pathHash}-${buffer.length}`;
    }

    /**
     * Get cache statistics
     */
    public getCacheStats(): CacheStats {
        const total = CachedAnalysisComponents.cacheStats.memoryHits + CachedAnalysisComponents.cacheStats.memoryMisses;
        CachedAnalysisComponents.cacheStats.hitRate = total > 0 
            ? (CachedAnalysisComponents.cacheStats.memoryHits / total) * 100 
            : 0;
        
        return { ...CachedAnalysisComponents.cacheStats };
    }

    /**
     * Clear all caches
     */
    public clearCache(): void {
        CachedAnalysisComponents.memoryCache.clear();
        CachedAnalysisComponents.cacheStats = {
            memoryHits: 0,
            memoryMisses: 0,
            memorySize: 0,
            hitRate: 0,
            averageRetrievalTime: 0
        };
    }

    /**
     * Warm up cache with common patterns
     */
    public async warmUpCache(commonFiles: Array<{ filePath: string; buffer: Buffer }>): Promise<void> {
        console.log(`Warming up cache with ${commonFiles.length} files...`);
        
        for (const file of commonFiles) {
            const cacheKey = this.generateCacheKey(file.filePath, file.buffer);
            
            // Pre-generate cache keys and basic metadata
            const basicMetadata = {
                filePath: file.filePath,
                fileSize: file.buffer.length,
                cacheKey,
                preWarmed: true
            };
            
            // Store basic info for faster lookups
            CachedAnalysisComponents.memoryCache.set(`${cacheKey}-meta`, {
                data: basicMetadata,
                timestamp: Date.now()
            });
        }
    }
}

/**
 * Memory-Efficient Thumbnail Extractor
 */
export class MemoryEfficientThumbnailExtractor {
    private static thumbnailCache = new Map<string, any>();
    private static readonly MAX_CACHE_SIZE = 500;

    /**
     * Extract thumbnails with memory optimization
     */
    public async extractThumbnails(
        s4tkPackage: Package | null,
        filePath: string,
        options: any = {},
        cancellationToken?: any
    ): Promise<{ thumbnails: any[] }> {
        if (!s4tkPackage) {
            return { thumbnails: [] };
        }

        const cacheKey = `${filePath}-${s4tkPackage.size}`;
        
        // Check cache
        const cached = this.thumbnailCache.get(cacheKey);
        if (cached && (Date.now() - cached.timestamp) < 30 * 60 * 1000) { // 30 min cache
            return { thumbnails: cached.thumbnails };
        }

        try {
            const thumbnails = await this.extractThumbnailsOptimized(s4tkPackage, options, cancellationToken);
            
            // Cache result
            this.thumbnailCache.set(cacheKey, {
                thumbnails,
                timestamp: Date.now()
            });

            // Limit cache size
            if (this.thumbnailCache.size > this.MAX_CACHE_SIZE) {
                const entries = Array.from(this.thumbnailCache.entries());
                entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
                
                // Remove oldest 20%
                const toRemove = Math.floor(entries.length * 0.2);
                for (let i = 0; i < toRemove; i++) {
                    this.thumbnailCache.delete(entries[i][0]);
                }
            }

            return { thumbnails };

        } catch (error) {
            console.warn('Thumbnail extraction error:', error);
            return { thumbnails: [] };
        }
    }

    /**
     * Optimized thumbnail extraction
     */
    private async extractThumbnailsOptimized(
        s4tkPackage: Package,
        options: any,
        cancellationToken?: any
    ): Promise<any[]> {
        const thumbnails: any[] = [];
        const maxThumbnails = 3; // Limit for performance
        let extractedCount = 0;

        // Priority thumbnail types
        const thumbnailTypes = [0x3C1AF1F2, 0x2E75C764]; // CAS Thumbnail, PNG

        for (const [key, entry] of s4tkPackage.entries()) {
            if (cancellationToken?.isCancelled) break;
            if (extractedCount >= maxThumbnails) break;
            
            if (thumbnailTypes.includes(key.type)) {
                try {
                    const thumbnail = await this.extractSingleThumbnail(entry, key, extractedCount);
                    if (thumbnail) {
                        thumbnails.push(thumbnail);
                        extractedCount++;
                    }
                } catch (error) {
                    // Continue with next thumbnail
                    console.warn('Single thumbnail extraction failed:', error);
                }
            }
        }

        return thumbnails;
    }

    /**
     * Extract single thumbnail
     */
    private async extractSingleThumbnail(entry: any, key: any, index: number): Promise<any | null> {
        try {
            const buffer = entry.value;
            if (!buffer || buffer.length === 0) return null;

            // Quick thumbnail data
            const base64Data = `data:image/png;base64,${buffer.toString('base64')}`;

            return {
                id: `thumb-${index}-${key.instance.toString(16)}`,
                resourceType: key.type === 0x3C1AF1F2 ? 'CAS Thumbnail' : 'PNG Image',
                imageData: base64Data,
                format: 'png',
                width: 256,
                height: 256,
                category: 'Thumbnail',
                confidence: 85,
                extractionMethod: 'optimized',
                fileSize: buffer.length,
                isHighQuality: buffer.length > 10240
            };

        } catch (error) {
            return null;
        }
    }

    /**
     * Clear thumbnail cache
     */
    public static clearCache(): void {
        this.thumbnailCache.clear();
    }
}
