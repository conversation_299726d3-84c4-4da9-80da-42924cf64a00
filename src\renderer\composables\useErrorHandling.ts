/**
 * Vue 3 Composable: Error Handling State Management
 * Centralized error state management with different error types and severity levels
 * Follows Vue 3 Composition API best practices with proper reactivity patterns
 */

import { ref, computed, reactive } from 'vue';

export type ErrorSeverity = 'info' | 'warning' | 'error' | 'critical';
export type ErrorCategory = 'analysis' | 'file' | 'network' | 'validation' | 'system' | 'user';

export interface AppError {
  id: string;
  message: string;
  severity: ErrorSeverity;
  category: ErrorCategory;
  timestamp: Date;
  details?: string;
  stack?: string;
  context?: Record<string, any>;
  dismissed?: boolean;
  autoHide?: boolean;
  duration?: number; // in milliseconds
}

export interface ErrorStats {
  total: number;
  byCategory: Record<ErrorCategory, number>;
  bySeverity: Record<ErrorSeverity, number>;
  recent: number; // errors in last 5 minutes
}

export function useErrorHandling() {
  // Reactive state
  const errors = ref<AppError[]>([]);
  const isErrorPanelOpen = ref(false);
  const maxErrors = ref(100); // Prevent memory issues
  
  // Computed properties
  const hasErrors = computed(() => errors.value.length > 0);
  const visibleErrors = computed(() => errors.value.filter(error => !error.dismissed));
  const criticalErrors = computed(() => errors.value.filter(error => error.severity === 'critical'));
  const recentErrors = computed(() => {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return errors.value.filter(error => error.timestamp > fiveMinutesAgo);
  });
  
  const errorStats = computed((): ErrorStats => {
    const stats: ErrorStats = {
      total: errors.value.length,
      byCategory: {
        analysis: 0,
        file: 0,
        network: 0,
        validation: 0,
        system: 0,
        user: 0,
      },
      bySeverity: {
        info: 0,
        warning: 0,
        error: 0,
        critical: 0,
      },
      recent: recentErrors.value.length,
    };
    
    errors.value.forEach(error => {
      stats.byCategory[error.category]++;
      stats.bySeverity[error.severity]++;
    });
    
    return stats;
  });
  
  // Error management methods
  const generateErrorId = (): string => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };
  
  const addError = (
    message: string,
    severity: ErrorSeverity = 'error',
    category: ErrorCategory = 'system',
    options: Partial<AppError> = {}
  ): string => {
    const errorId = generateErrorId();
    
    const newError: AppError = {
      id: errorId,
      message,
      severity,
      category,
      timestamp: new Date(),
      dismissed: false,
      autoHide: severity === 'info' || severity === 'warning',
      duration: severity === 'info' ? 3000 : severity === 'warning' ? 5000 : undefined,
      ...options,
    };
    
    console.log(`🚨 [useErrorHandling] ${severity.toUpperCase()}: ${message}`, newError);
    
    // Add to beginning of array (most recent first)
    errors.value.unshift(newError);
    
    // Limit total errors to prevent memory issues
    if (errors.value.length > maxErrors.value) {
      errors.value = errors.value.slice(0, maxErrors.value);
    }
    
    // Auto-hide if configured
    if (newError.autoHide && newError.duration) {
      setTimeout(() => {
        dismissError(errorId);
      }, newError.duration);
    }
    
    return errorId;
  };
  
  const dismissError = (errorId: string) => {
    const errorIndex = errors.value.findIndex(error => error.id === errorId);
    if (errorIndex !== -1) {
      errors.value[errorIndex].dismissed = true;
    }
  };
  
  const removeError = (errorId: string) => {
    const errorIndex = errors.value.findIndex(error => error.id === errorId);
    if (errorIndex !== -1) {
      errors.value.splice(errorIndex, 1);
    }
  };
  
  const clearErrors = (category?: ErrorCategory, severity?: ErrorSeverity) => {
    if (category || severity) {
      errors.value = errors.value.filter(error => {
        if (category && error.category !== category) return true;
        if (severity && error.severity !== severity) return true;
        return false;
      });
    } else {
      errors.value = [];
    }
  };
  
  const clearDismissedErrors = () => {
    errors.value = errors.value.filter(error => !error.dismissed);
  };
  
  // Specialized error methods
  const addAnalysisError = (message: string, details?: string, context?: Record<string, any>) => {
    return addError(message, 'error', 'analysis', { details, context });
  };
  
  const addFileError = (message: string, filePath?: string, details?: string) => {
    return addError(message, 'error', 'file', { 
      details, 
      context: filePath ? { filePath } : undefined 
    });
  };
  
  const addValidationError = (message: string, field?: string, value?: any) => {
    return addError(message, 'warning', 'validation', {
      context: field ? { field, value } : undefined
    });
  };
  
  const addNetworkError = (message: string, url?: string, status?: number) => {
    return addError(message, 'error', 'network', {
      context: { url, status }
    });
  };
  
  const addCriticalError = (message: string, error?: Error) => {
    return addError(message, 'critical', 'system', {
      details: error?.message,
      stack: error?.stack,
      context: { error: error?.name }
    });
  };
  
  const addInfo = (message: string, category: ErrorCategory = 'system') => {
    return addError(message, 'info', category, { autoHide: true, duration: 3000 });
  };
  
  const addWarning = (message: string, category: ErrorCategory = 'system') => {
    return addError(message, 'warning', category, { autoHide: true, duration: 5000 });
  };
  
  // Error panel management
  const openErrorPanel = () => {
    isErrorPanelOpen.value = true;
  };
  
  const closeErrorPanel = () => {
    isErrorPanelOpen.value = false;
  };
  
  const toggleErrorPanel = () => {
    isErrorPanelOpen.value = !isErrorPanelOpen.value;
  };
  
  // Utility methods
  const getErrorsByCategory = (category: ErrorCategory) => {
    return errors.value.filter(error => error.category === category);
  };
  
  const getErrorsBySeverity = (severity: ErrorSeverity) => {
    return errors.value.filter(error => error.severity === severity);
  };
  
  const exportErrors = () => {
    const exportData = {
      timestamp: new Date().toISOString(),
      stats: errorStats.value,
      errors: errors.value.map(error => ({
        ...error,
        timestamp: error.timestamp.toISOString(),
      })),
    };
    
    return JSON.stringify(exportData, null, 2);
  };
  
  return {
    // State
    errors,
    isErrorPanelOpen,
    maxErrors,
    
    // Computed
    hasErrors,
    visibleErrors,
    criticalErrors,
    recentErrors,
    errorStats,
    
    // Methods
    addError,
    dismissError,
    removeError,
    clearErrors,
    clearDismissedErrors,
    
    // Specialized methods
    addAnalysisError,
    addFileError,
    addValidationError,
    addNetworkError,
    addCriticalError,
    addInfo,
    addWarning,
    
    // Panel management
    openErrorPanel,
    closeErrorPanel,
    toggleErrorPanel,
    
    // Utilities
    getErrorsByCategory,
    getErrorsBySeverity,
    exportErrors,
  };
}
